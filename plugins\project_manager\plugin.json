{"id": "project_manager", "name": "项目管理器", "version": "1.0.0", "description": "提供项目管理功能，包括项目创建、打开和管理", "author": "Framework Developer", "main": "main.py", "display_name": "项目管理器", "icon": "icon.png", "category": "工具", "keywords": ["项目", "管理", "文件"], "activation_events": ["*"], "contributes": {"commands": [{"command": "project_manager.new_project", "title": "新建项目", "description": "创建一个新项目"}, {"command": "project_manager.open_project", "title": "打开项目", "description": "打开现有项目"}], "menus": [{"command": "project_manager.new_project", "group": "file", "when": "always"}], "views": [{"id": "project_explorer", "name": "项目资源管理器", "when": "always"}]}}