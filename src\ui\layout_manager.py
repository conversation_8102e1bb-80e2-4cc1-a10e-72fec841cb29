"""
布局管理器 - 管理应用程序的布局和组件
"""
from typing import Dict, Any, Optional, List
from PySide6.QtWidgets import QWidget, QSplitter
from PySide6.QtCore import QObject, Signal, Qt

from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import exception_handler_decorator


class LayoutManager(QObject):
    """布局管理器"""
    
    # 布局信号
    layout_changed = Signal(dict)
    component_added = Signal(str, object)
    component_removed = Signal(str)
    
    def __init__(self, main_window):
        super().__init__()
        self._main_window = main_window
        self._components: Dict[str, QWidget] = {}
        self._layout_config = {}
        
        # 连接主窗口信号
        main_window.layout_changed.connect(self._on_main_layout_changed)
        
        # 订阅事件
        event_system.subscribe('layout_restore_requested', self._on_layout_restore)
        event_system.subscribe('layout_save_requested', self._on_layout_save)
    
    @exception_handler_decorator()
    def register_component(self, name: str, component: QWidget, 
                          area: str = 'workspace', **kwargs):
        """注册组件到布局管理器"""
        self._components[name] = component
        
        # 根据区域添加组件
        if area == 'sidebar':
            self._add_to_sidebar(component, **kwargs)
        elif area == 'workspace':
            self._add_to_workspace(component, **kwargs)
        elif area == 'statusbar':
            self._add_to_statusbar(component, **kwargs)
        
        # 发布组件添加事件
        self.component_added.emit(name, component)
        event_system.emit('component_registered', {
            'name': name,
            'component': component,
            'area': area
        }, source=self)
    
    def unregister_component(self, name: str):
        """从布局管理器注销组件"""
        if name in self._components:
            component = self._components[name]
            
            # 从父组件移除
            if component.parent():
                component.setParent(None)
            
            # 从字典移除
            del self._components[name]
            
            # 发布组件移除事件
            self.component_removed.emit(name)
            event_system.emit('component_unregistered', {
                'name': name
            }, source=self)
    
    def _add_to_sidebar(self, component: QWidget, **kwargs):
        """添加组件到侧边栏"""
        sidebar = self._main_window.get_sidebar()
        if sidebar:
            # 如果侧边栏是自定义组件，调用其添加方法
            if hasattr(sidebar, 'add_component'):
                sidebar.add_component(component, **kwargs)
            else:
                # 否则直接设置为子组件
                component.setParent(sidebar)
    
    def _add_to_workspace(self, component: QWidget, **kwargs):
        """添加组件到工作区"""
        workspace = self._main_window.get_workspace()
        if workspace:
            # 如果工作区是自定义组件，调用其添加方法
            if hasattr(workspace, 'add_component'):
                workspace.add_component(component, **kwargs)
            else:
                # 否则直接设置为子组件
                component.setParent(workspace)
    
    def _add_to_statusbar(self, component: QWidget, **kwargs):
        """添加组件到状态栏"""
        statusbar = self._main_window.statusBar()
        if statusbar:
            permanent = kwargs.get('permanent', False)
            if permanent:
                statusbar.addPermanentWidget(component)
            else:
                statusbar.addWidget(component)
    
    def get_component(self, name: str) -> Optional[QWidget]:
        """获取注册的组件"""
        return self._components.get(name)
    
    def get_all_components(self) -> Dict[str, QWidget]:
        """获取所有注册的组件"""
        return self._components.copy()
    
    def set_component_visible(self, name: str, visible: bool):
        """设置组件可见性"""
        component = self.get_component(name)
        if component:
            component.setVisible(visible)
            
            # 发布可见性变化事件
            event_system.emit('component_visibility_changed', {
                'name': name,
                'visible': visible
            }, source=self)
    
    def toggle_component_visibility(self, name: str):
        """切换组件可见性"""
        component = self.get_component(name)
        if component:
            self.set_component_visible(name, not component.isVisible())
    
    def save_layout(self) -> Dict[str, Any]:
        """保存当前布局状态"""
        layout_state = {
            'window_geometry': {
                'x': self._main_window.x(),
                'y': self._main_window.y(),
                'width': self._main_window.width(),
                'height': self._main_window.height(),
                'maximized': self._main_window.isMaximized()
            },
            'components': {}
        }
        
        # 保存组件状态
        for name, component in self._components.items():
            component_state = {
                'visible': component.isVisible(),
                'geometry': {
                    'x': component.x(),
                    'y': component.y(),
                    'width': component.width(),
                    'height': component.height()
                }
            }
            
            # 如果组件有自定义状态保存方法
            if hasattr(component, 'save_state'):
                component_state['custom'] = component.save_state()
            
            layout_state['components'][name] = component_state
        
        # 保存分割器状态
        main_splitter = self._main_window._main_splitter
        if main_splitter:
            layout_state['splitter_sizes'] = main_splitter.sizes()
        
        self._layout_config = layout_state
        return layout_state
    
    def restore_layout(self, layout_state: Optional[Dict[str, Any]] = None):
        """恢复布局状态"""
        if layout_state is None:
            layout_state = self._layout_config
        
        if not layout_state:
            return
        
        # 恢复窗口几何状态
        window_geo = layout_state.get('window_geometry', {})
        if window_geo:
            self._main_window.setGeometry(
                window_geo.get('x', 100),
                window_geo.get('y', 100),
                window_geo.get('width', 1200),
                window_geo.get('height', 800)
            )
            
            if window_geo.get('maximized', False):
                self._main_window.showMaximized()
        
        # 恢复组件状态
        components_state = layout_state.get('components', {})
        for name, state in components_state.items():
            component = self.get_component(name)
            if component:
                # 恢复可见性
                component.setVisible(state.get('visible', True))
                
                # 恢复几何状态
                geo = state.get('geometry', {})
                if geo:
                    component.setGeometry(
                        geo.get('x', 0),
                        geo.get('y', 0),
                        geo.get('width', 200),
                        geo.get('height', 200)
                    )
                
                # 恢复自定义状态
                if 'custom' in state and hasattr(component, 'restore_state'):
                    component.restore_state(state['custom'])
        
        # 恢复分割器状态
        splitter_sizes = layout_state.get('splitter_sizes')
        if splitter_sizes and self._main_window._main_splitter:
            self._main_window._main_splitter.setSizes(splitter_sizes)
    
    def reset_layout(self):
        """重置布局到默认状态"""
        # 重置窗口大小
        default_width = config.get('window.width', 1200)
        default_height = config.get('window.height', 800)
        self._main_window.resize(default_width, default_height)
        
        # 重置分割器
        sidebar_width = config.get('sidebar.default_width', 300)
        workspace_width = default_width - sidebar_width
        
        if self._main_window._main_splitter:
            self._main_window._main_splitter.setSizes([sidebar_width, workspace_width])
        
        # 重置所有组件可见性
        for component in self._components.values():
            component.setVisible(True)
        
        # 发布布局重置事件
        event_system.emit('layout_reset', source=self)
    
    def _on_main_layout_changed(self):
        """主布局变化处理"""
        # 保存当前布局状态
        layout_state = self.save_layout()
        
        # 发布布局变化信号
        self.layout_changed.emit(layout_state)
        
        # 发布事件
        event_system.emit('layout_changed', layout_state, source=self)
    
    def _on_layout_restore(self, event):
        """处理布局恢复请求"""
        layout_data = event.data
        self.restore_layout(layout_data)
    
    def _on_layout_save(self, event):
        """处理布局保存请求"""
        layout_state = self.save_layout()
        
        # 保存到配置
        config.set('layout.last_state', layout_state)
        config.save_config()
        
        # 发布保存完成事件
        event_system.emit('layout_saved', layout_state, source=self)
    
    def apply_responsive_layout(self):
        """应用响应式布局"""
        window_width = self._main_window.width()
        
        # 根据窗口宽度调整布局
        if window_width < 800:
            # 小屏幕：隐藏侧边栏
            sidebar = self._main_window.get_sidebar()
            if sidebar and hasattr(sidebar, 'collapse'):
                sidebar.collapse()
        elif window_width > 1400:
            # 大屏幕：展开侧边栏
            sidebar = self._main_window.get_sidebar()
            if sidebar and hasattr(sidebar, 'expand'):
                sidebar.expand()
    
    def get_layout_info(self) -> Dict[str, Any]:
        """获取布局信息"""
        return {
            'components_count': len(self._components),
            'components': list(self._components.keys()),
            'window_size': (self._main_window.width(), self._main_window.height()),
            'has_layout_config': bool(self._layout_config)
        }
