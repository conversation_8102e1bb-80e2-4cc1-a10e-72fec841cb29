"""
全局异常处理器 - 统一异常管理和错误处理
"""
import sys
import traceback
import logging
from typing import Callable, Optional, Any, Dict
from datetime import datetime
from pathlib import Path
from PySide6.QtCore import QObject, Signal
from .event_system import event_system, EventPriority


class ExceptionLevel:
    """异常级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ExceptionInfo:
    """异常信息类"""
    
    def __init__(self, exception: Exception, level: str = ExceptionLevel.ERROR,
                 context: Optional[Dict[str, Any]] = None):
        self.exception = exception
        self.level = level
        self.context = context or {}
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()
        self.exception_type = type(exception).__name__
        self.message = str(exception)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'level': self.level,
            'exception_type': self.exception_type,
            'message': self.message,
            'traceback': self.traceback,
            'context': self.context
        }


class ExceptionHandler(QObject):
    """全局异常处理器"""
    
    # 异常信号
    exception_occurred = Signal(object)  # ExceptionInfo
    
    def __init__(self):
        super().__init__()
        self._logger = None
        self._handlers: Dict[str, Callable] = {}
        self._setup_logging()
        self._install_global_handler()
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "exceptions.log", encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 配置logger
        self._logger = logging.getLogger('ExceptionHandler')
        self._logger.setLevel(logging.DEBUG)
        self._logger.addHandler(file_handler)
        self._logger.addHandler(console_handler)
    
    def _install_global_handler(self):
        """安装全局异常处理器"""
        # 设置Python全局异常处理器
        sys.excepthook = self._global_exception_hook

        # 设置线程异常处理器
        import threading
        if hasattr(threading, 'excepthook'):
            threading.excepthook = self._thread_exception_hook
    
    def _global_exception_hook(self, exc_type, exc_value, exc_traceback):
        """全局异常钩子"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        exception_info = ExceptionInfo(
            exc_value, 
            ExceptionLevel.CRITICAL,
            {'source': 'global_hook'}
        )
        self.handle_exception(exception_info)
    
    def _thread_exception_hook(self, args):
        """线程异常钩子"""
        exception_info = ExceptionInfo(
            args.exc_value,
            ExceptionLevel.ERROR,
            {'source': 'thread', 'thread_name': args.thread.name}
        )
        self.handle_exception(exception_info)
    
    def handle_exception(self, exception_info: ExceptionInfo):
        """处理异常"""
        try:
            # 记录日志
            self._log_exception(exception_info)
            
            # 发送异常信号
            self.exception_occurred.emit(exception_info)
            
            # 发布异常事件
            event_system.emit(
                'exception_occurred',
                exception_info,
                source=self,
                priority=EventPriority.HIGH,
                immediate=True
            )
            
            # 调用特定异常处理器
            exception_type = exception_info.exception_type
            if exception_type in self._handlers:
                self._handlers[exception_type](exception_info)
            
        except Exception as e:
            # 异常处理器本身出错时的备用处理
            print(f"异常处理器错误: {e}")
            traceback.print_exc()
    
    def _log_exception(self, exception_info: ExceptionInfo):
        """记录异常日志"""
        level_map = {
            ExceptionLevel.DEBUG: logging.DEBUG,
            ExceptionLevel.INFO: logging.INFO,
            ExceptionLevel.WARNING: logging.WARNING,
            ExceptionLevel.ERROR: logging.ERROR,
            ExceptionLevel.CRITICAL: logging.CRITICAL
        }
        
        log_level = level_map.get(exception_info.level, logging.ERROR)
        
        message = (
            f"异常类型: {exception_info.exception_type}\n"
            f"异常消息: {exception_info.message}\n"
            f"上下文: {exception_info.context}\n"
            f"堆栈跟踪:\n{exception_info.traceback}"
        )
        
        self._logger.log(log_level, message)
    
    def register_handler(self, exception_type: str, handler: Callable):
        """注册特定异常类型的处理器"""
        self._handlers[exception_type] = handler
    
    def unregister_handler(self, exception_type: str):
        """取消注册异常处理器"""
        if exception_type in self._handlers:
            del self._handlers[exception_type]
    
    def safe_call(self, func: Callable, *args, **kwargs) -> Any:
        """安全调用函数，自动捕获异常"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            exception_info = ExceptionInfo(
                e, 
                ExceptionLevel.ERROR,
                {'function': func.__name__, 'args': args, 'kwargs': kwargs}
            )
            self.handle_exception(exception_info)
            return None


def safe_call(func: Callable, *args, **kwargs) -> Any:
    """装饰器：安全调用函数"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            exception_info = ExceptionInfo(
                e,
                ExceptionLevel.ERROR,
                {'function': func.__name__}
            )
            global_exception_handler.handle_exception(exception_info)
            return None
    return wrapper


def exception_handler_decorator(level: str = ExceptionLevel.ERROR):
    """异常处理装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                exception_info = ExceptionInfo(
                    e, level, {'function': func.__name__}
                )
                global_exception_handler.handle_exception(exception_info)
                return None
        return wrapper
    return decorator


# 全局异常处理器实例
global_exception_handler = ExceptionHandler()
