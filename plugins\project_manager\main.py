"""
项目管理器插件 - 示例插件实现
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, 
                               QTreeWidgetItem, QPushButton, QLabel, QFileDialog,
                               QMessageBox, QInputDialog, QMenu)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QAction

import os
import json
from pathlib import Path
from typing import Optional

# 导入插件接口
import sys
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from plugins.plugin_interface import IPlugin, PluginContext


class ProjectExplorer(QWidget):
    """项目资源管理器组件"""
    
    # 信号
    file_opened = Signal(str)  # 文件路径
    project_changed = Signal(str)  # 项目路径
    
    def __init__(self):
        super().__init__()
        
        self._current_project_path = None
        self._tree_widget = None
        
        self._setup_ui()
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("项目资源管理器")
        title_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        title_layout.addWidget(title_label)
        
        # 新建项目按钮
        new_btn = QPushButton("新建")
        new_btn.setMaximumWidth(50)
        new_btn.clicked.connect(self._new_project)
        title_layout.addWidget(new_btn)
        
        # 打开项目按钮
        open_btn = QPushButton("打开")
        open_btn.setMaximumWidth(50)
        open_btn.clicked.connect(self._open_project)
        title_layout.addWidget(open_btn)
        
        layout.addLayout(title_layout)
        
        # 项目树
        self._tree_widget = QTreeWidget()
        self._tree_widget.setHeaderHidden(True)
        self._tree_widget.itemDoubleClicked.connect(self._on_item_double_clicked)
        self._tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self._tree_widget.customContextMenuRequested.connect(self._show_context_menu)
        
        layout.addWidget(self._tree_widget)
        
        # 状态标签
        self._status_label = QLabel("未打开项目")
        self._status_label.setStyleSheet("color: #666666; font-size: 10px;")
        layout.addWidget(self._status_label)
    
    def _new_project(self):
        """新建项目"""
        # 选择项目目录
        project_dir = QFileDialog.getExistingDirectory(
            self, "选择项目目录", str(Path.home())
        )
        
        if not project_dir:
            return
        
        # 输入项目名称
        project_name, ok = QInputDialog.getText(
            self, "新建项目", "项目名称:", text=Path(project_dir).name
        )
        
        if not ok or not project_name:
            return
        
        # 创建项目文件
        project_file = Path(project_dir) / f"{project_name}.project"
        project_data = {
            "name": project_name,
            "version": "1.0.0",
            "description": "",
            "created": str(Path(project_dir).stat().st_ctime),
            "files": []
        }
        
        try:
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, indent=2, ensure_ascii=False)
            
            # 打开新项目
            self.open_project(str(project_file))
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建项目失败: {e}")
    
    def _open_project(self):
        """打开项目"""
        project_file, _ = QFileDialog.getOpenFileName(
            self, "打开项目", str(Path.home()), "项目文件 (*.project)"
        )
        
        if project_file:
            self.open_project(project_file)
    
    def open_project(self, project_path: str):
        """打开指定项目"""
        try:
            project_file = Path(project_path)
            if not project_file.exists():
                QMessageBox.warning(self, "警告", "项目文件不存在")
                return
            
            # 读取项目文件
            with open(project_file, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            self._current_project_path = str(project_file.parent)
            
            # 更新状态
            self._status_label.setText(f"项目: {project_data.get('name', '未命名')}")
            
            # 刷新项目树
            self._refresh_project_tree()
            
            # 发布项目变化事件
            self.project_changed.emit(self._current_project_path)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败: {e}")
    
    def _refresh_project_tree(self):
        """刷新项目树"""
        self._tree_widget.clear()
        
        if not self._current_project_path:
            return
        
        project_path = Path(self._current_project_path)
        
        # 创建根节点
        root_item = QTreeWidgetItem([project_path.name])
        root_item.setData(0, Qt.UserRole, str(project_path))
        self._tree_widget.addTopLevelItem(root_item)
        
        # 递归添加文件和目录
        self._add_directory_items(root_item, project_path)
        
        # 展开根节点
        root_item.setExpanded(True)
    
    def _add_directory_items(self, parent_item: QTreeWidgetItem, directory: Path):
        """递归添加目录项"""
        try:
            # 先添加目录
            for item in sorted(directory.iterdir()):
                if item.is_dir() and not item.name.startswith('.'):
                    dir_item = QTreeWidgetItem([item.name])
                    dir_item.setData(0, Qt.UserRole, str(item))
                    parent_item.addChild(dir_item)
                    
                    # 递归添加子目录（限制深度）
                    if len(str(item).split(os.sep)) - len(str(directory).split(os.sep)) < 3:
                        self._add_directory_items(dir_item, item)
            
            # 再添加文件
            for item in sorted(directory.iterdir()):
                if item.is_file() and not item.name.startswith('.'):
                    file_item = QTreeWidgetItem([item.name])
                    file_item.setData(0, Qt.UserRole, str(item))
                    parent_item.addChild(file_item)
        
        except PermissionError:
            # 忽略权限错误
            pass
    
    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """项目双击处理"""
        file_path = item.data(0, Qt.UserRole)
        if file_path and Path(file_path).is_file():
            self.file_opened.emit(file_path)
    
    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self._tree_widget.itemAt(position)
        if not item:
            return
        
        file_path = item.data(0, Qt.UserRole)
        if not file_path:
            return
        
        menu = QMenu(self)
        
        path_obj = Path(file_path)
        
        if path_obj.is_file():
            # 文件菜单
            open_action = QAction("打开", self)
            open_action.triggered.connect(lambda: self.file_opened.emit(file_path))
            menu.addAction(open_action)
        
        elif path_obj.is_dir():
            # 目录菜单
            refresh_action = QAction("刷新", self)
            refresh_action.triggered.connect(self._refresh_project_tree)
            menu.addAction(refresh_action)
        
        menu.exec_(self._tree_widget.mapToGlobal(position))


class ProjectManagerPlugin(IPlugin):
    """项目管理器插件"""
    
    def __init__(self):
        super().__init__()
        self._project_explorer = None
    
    def activate(self, context: PluginContext):
        """激活插件"""
        # 创建项目资源管理器
        self._project_explorer = ProjectExplorer()
        
        # 注册侧边栏面板
        context.register_sidebar_panel(
            "project_explorer",
            self._project_explorer,
            "项目",
            "📁"
        )
        
        # 注册命令
        context.register_command(
            "new_project",
            self._new_project,
            "新建项目",
            "创建一个新项目"
        )
        
        context.register_command(
            "open_project", 
            self._open_project,
            "打开项目",
            "打开现有项目"
        )
        
        # 订阅事件
        context.subscribe_event('new_project_requested', self._on_new_project_requested)
        context.subscribe_event('open_file_requested', self._on_open_file_requested)
        
        # 连接信号
        self._project_explorer.file_opened.connect(self._on_file_opened)
        self._project_explorer.project_changed.connect(self._on_project_changed)
        
        print("项目管理器插件已激活")
    
    def deactivate(self):
        """停用插件"""
        if self._project_explorer:
            self._project_explorer.deleteLater()
            self._project_explorer = None
        
        print("项目管理器插件已停用")
    
    def _new_project(self):
        """新建项目命令"""
        if self._project_explorer:
            self._project_explorer._new_project()
    
    def _open_project(self):
        """打开项目命令"""
        if self._project_explorer:
            self._project_explorer._open_project()
    
    def _on_new_project_requested(self, event):
        """新建项目请求处理"""
        self._new_project()
    
    def _on_open_file_requested(self, event):
        """打开文件请求处理"""
        # 这里可以实现文件打开逻辑
        pass
    
    def _on_file_opened(self, file_path: str):
        """文件打开处理"""
        # 发布文件打开事件
        self.context.emit_event('file_open_requested', {
            'file_path': file_path,
            'source': 'project_manager'
        })
    
    def _on_project_changed(self, project_path: str):
        """项目变化处理"""
        # 发布项目变化事件
        self.context.emit_event('project_changed', {
            'project_path': project_path,
            'source': 'project_manager'
        })
