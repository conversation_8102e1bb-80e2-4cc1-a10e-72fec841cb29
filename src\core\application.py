"""
主应用程序类 - 应用程序生命周期管理
"""
import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QObject, Signal
from PySide6.QtGui import QIcon

from .config_manager import config
from .event_system import event_system
from .exception_handler import global_exception_handler, exception_handler_decorator


class Application(QObject):
    """主应用程序类"""
    
    # 应用程序信号
    starting = Signal()
    started = Signal()
    stopping = Signal()
    stopped = Signal()
    
    def __init__(self):
        super().__init__()
        self._qt_app = None
        self._main_window = None
        self._event_timer = None
        self._is_running = False
        
        # 连接异常处理器信号
        global_exception_handler.exception_occurred.connect(self._on_exception)
    
    @exception_handler_decorator()
    def initialize(self):
        """初始化应用程序"""
        # 创建Qt应用程序
        self._qt_app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app_name = config.get('app.name', 'PySide6 Framework')
        app_version = config.get('app.version', '1.0.0')
        
        self._qt_app.setApplicationName(app_name)
        self._qt_app.setApplicationVersion(app_version)
        self._qt_app.setOrganizationName("Framework Developer")
        
        # 设置应用程序图标
        icon_path = Path("resources/icons/app.ico")
        if icon_path.exists():
            self._qt_app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置样式
        self._setup_style()
        
        # 创建事件处理定时器
        self._event_timer = QTimer()
        self._event_timer.timeout.connect(self._process_events)
        self._event_timer.start(16)  # 约60FPS
        
        # 发布初始化完成事件
        event_system.emit('app_initialized', source=self)
    
    def _setup_style(self):
        """设置应用程序样式"""
        theme = config.get('theme.default', 'light')
        
        if theme == 'dark':
            self._apply_dark_theme()
        else:
            self._apply_light_theme()
    
    def _apply_light_theme(self):
        """应用浅色主题"""
        style = """
        QMainWindow {
            background-color: #f0f0f0;
            color: #333333;
        }
        
        QWidget {
            background-color: #ffffff;
            color: #333333;
            border: none;
        }
        
        QPushButton {
            background-color: #e0e0e0;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 6px 12px;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #d0d0d0;
            border-color: #999999;
        }
        
        QPushButton:pressed {
            background-color: #c0c0c0;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        
        QTabBar::tab {
            background-color: #e0e0e0;
            border: 1px solid #cccccc;
            padding: 6px 12px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom: none;
        }
        
        QSplitter::handle {
            background-color: #cccccc;
        }
        
        QSplitter::handle:horizontal {
            width: 3px;
        }
        
        QSplitter::handle:vertical {
            height: 3px;
        }
        """
        self._qt_app.setStyleSheet(style)
    
    def _apply_dark_theme(self):
        """应用深色主题"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QWidget {
            background-color: #3c3c3c;
            color: #ffffff;
            border: none;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 4px;
            padding: 6px 12px;
            min-height: 20px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
            border-color: #888888;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QTabWidget::pane {
            border: 1px solid #666666;
            background-color: #3c3c3c;
        }
        
        QTabBar::tab {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            padding: 6px 12px;
            margin-right: 2px;
            color: #ffffff;
        }
        
        QTabBar::tab:selected {
            background-color: #3c3c3c;
            border-bottom: none;
        }
        
        QSplitter::handle {
            background-color: #666666;
        }
        
        QSplitter::handle:horizontal {
            width: 3px;
        }
        
        QSplitter::handle:vertical {
            height: 3px;
        }
        """
        self._qt_app.setStyleSheet(style)
    
    def _process_events(self):
        """处理事件队列"""
        event_system.process_events()
    
    def _on_exception(self, exception_info):
        """处理异常信号"""
        print(f"应用程序异常: {exception_info.message}")
        
        # 发布异常事件给UI层处理
        event_system.emit('ui_exception', exception_info, source=self)
    
    def set_main_window(self, window):
        """设置主窗口"""
        self._main_window = window
        
        # 连接窗口关闭信号
        if hasattr(window, 'closeEvent'):
            original_close = window.closeEvent
            def close_wrapper(event):
                self.stop()
                original_close(event)
            window.closeEvent = close_wrapper
    
    @exception_handler_decorator()
    def run(self):
        """运行应用程序"""
        if not self._qt_app:
            self.initialize()
        
        self._is_running = True
        self.starting.emit()
        event_system.emit('app_starting', source=self)
        
        # 显示主窗口
        if self._main_window:
            self._main_window.show()
        
        self.started.emit()
        event_system.emit('app_started', source=self)
        
        # 运行Qt事件循环
        try:
            exit_code = self._qt_app.exec()
            return exit_code
        except Exception as e:
            print(f"应用程序运行错误: {e}")
            return 1
        finally:
            self._cleanup()
    
    def stop(self):
        """停止应用程序"""
        if not self._is_running:
            return
        
        self.stopping.emit()
        event_system.emit('app_stopping', source=self)
        
        self._is_running = False
        
        if self._qt_app:
            self._qt_app.quit()
        
        self.stopped.emit()
        event_system.emit('app_stopped', source=self)
    
    def _cleanup(self):
        """清理资源"""
        # 停止事件定时器
        if self._event_timer:
            self._event_timer.stop()
        
        # 停止配置文件监听
        config.stop_watching()
        
        # 清理事件系统
        event_system.clear_all()
    
    @property
    def qt_app(self):
        """获取Qt应用程序实例"""
        return self._qt_app
    
    @property
    def main_window(self):
        """获取主窗口"""
        return self._main_window
    
    @property
    def is_running(self):
        """检查应用程序是否正在运行"""
        return self._is_running


# 全局应用程序实例
app = Application()
