"""
工作区和标签页管理系统 - 中间工作区的标签页管理
"""
from typing import Dict, List, Optional, Any, Callable
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                               QTabBar, QPushButton, QLabel, QFrame, QMenu, 
                               QMessageBox, QSplitter)
from PySide6.QtCore import Qt, Signal, QPoint, QTimer
from PySide6.QtGui import QIcon, QPixmap, QMouseEvent, QContextMenuEvent, QAction

from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import exception_handler_decorator


class TabData:
    """标签页数据类"""
    
    def __init__(self, tab_id: str, title: str, widget: QWidget, 
                 icon: QIcon = None, closable: bool = True, 
                 modified: bool = False, tooltip: str = ""):
        self.tab_id = tab_id
        self.title = title
        self.widget = widget
        self.icon = icon
        self.closable = closable
        self.modified = modified
        self.tooltip = tooltip or title
        self.metadata = {}  # 额外的元数据


class CustomTabBar(QTabBar):
    """自定义标签栏"""
    
    # 信号
    tab_close_requested = Signal(int)
    tab_context_menu_requested = Signal(int, QPoint)
    tab_middle_clicked = Signal(int)
    
    def __init__(self):
        super().__init__()
        
        # 设置标签栏属性
        self.setTabsClosable(True)
        self.setMovable(True)
        self.setExpanding(False)
        self.setUsesScrollButtons(True)
        
        # 连接信号
        self.tabCloseRequested.connect(self.tab_close_requested.emit)
        
        # 设置样式
        self._setup_style()
    
    def _setup_style(self):
        """设置标签栏样式"""
        self.setStyleSheet("""
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-bottom: none;
                padding: 6px 12px;
                margin-right: 2px;
                min-width: 80px;
                max-width: 200px;
            }
            
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-color: #007acc;
                border-bottom: 2px solid #007acc;
            }
            
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
            
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
            
            QTabBar::close-button {
                image: url(resources/icons/close.png);
                subcontrol-position: right;
            }
            
            QTabBar::close-button:hover {
                background-color: #ff6b6b;
                border-radius: 2px;
            }
        """)
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MiddleButton:
            # 中键点击关闭标签页
            index = self.tabAt(event.pos())
            if index >= 0:
                self.tab_middle_clicked.emit(index)
                event.accept()
                return
        
        super().mousePressEvent(event)
    
    def contextMenuEvent(self, event: QContextMenuEvent):
        """右键菜单事件"""
        index = self.tabAt(event.pos())
        if index >= 0:
            self.tab_context_menu_requested.emit(index, event.globalPos())
        event.accept()


class TabWidget(QTabWidget):
    """自定义标签页组件"""
    
    # 信号
    tab_added = Signal(str, QWidget)  # tab_id, widget
    tab_removed = Signal(str)  # tab_id
    tab_changed = Signal(str)  # tab_id
    tab_close_requested = Signal(str)  # tab_id
    all_tabs_closed = Signal()
    
    def __init__(self):
        super().__init__()
        
        self._tabs_data: Dict[str, TabData] = {}
        self._tab_id_to_index: Dict[str, int] = {}
        
        # 创建自定义标签栏
        self._custom_tab_bar = CustomTabBar()
        self.setTabBar(self._custom_tab_bar)
        
        # 设置标签页位置
        self.setTabPosition(QTabWidget.North)
        self.setDocumentMode(True)
        
        # 连接信号
        self._setup_connections()
    
    def _setup_connections(self):
        """设置信号连接"""
        self.currentChanged.connect(self._on_current_changed)
        self._custom_tab_bar.tab_close_requested.connect(self._on_tab_close_requested)
        self._custom_tab_bar.tab_context_menu_requested.connect(self._on_context_menu)
        self._custom_tab_bar.tab_middle_clicked.connect(self._on_middle_click)
    
    @exception_handler_decorator()
    def add_tab(self, tab_id: str, widget: QWidget, title: str, 
                icon: QIcon = None, closable: bool = True, 
                tooltip: str = "", activate: bool = True) -> int:
        """添加标签页"""
        if tab_id in self._tabs_data:
            # 如果标签页已存在，激活它
            self.activate_tab(tab_id)
            return self._tab_id_to_index[tab_id]
        
        # 创建标签页数据
        tab_data = TabData(tab_id, title, widget, icon, closable, False, tooltip)
        
        # 添加到QTabWidget
        index = self.addTab(widget, title)
        
        # 设置图标和工具提示
        if icon:
            self.setTabIcon(index, icon)
        if tooltip:
            self.setTabToolTip(index, tooltip)
        
        # 设置关闭按钮
        if not closable:
            self.tabBar().setTabButton(index, QTabBar.RightSide, None)
        
        # 保存数据
        self._tabs_data[tab_id] = tab_data
        self._tab_id_to_index[tab_id] = index
        
        # 激活新标签页
        if activate:
            self.setCurrentIndex(index)
        
        # 发布事件
        self.tab_added.emit(tab_id, widget)
        event_system.emit('tab_added', {
            'tab_id': tab_id,
            'title': title,
            'widget': widget
        }, source=self)
        
        return index
    
    def remove_tab(self, tab_id: str) -> bool:
        """移除标签页"""
        if tab_id not in self._tabs_data:
            return False
        
        index = self._tab_id_to_index[tab_id]
        tab_data = self._tabs_data[tab_id]
        
        # 检查是否可以关闭
        if not tab_data.closable:
            return False
        
        # 检查是否有未保存的修改
        if tab_data.modified:
            result = self._confirm_close_modified_tab(tab_data)
            if not result:
                return False
        
        # 移除标签页
        self.removeTab(index)
        
        # 清理数据
        del self._tabs_data[tab_id]
        del self._tab_id_to_index[tab_id]
        
        # 更新其他标签页的索引
        self._update_tab_indices()
        
        # 发布事件
        self.tab_removed.emit(tab_id)
        event_system.emit('tab_removed', {
            'tab_id': tab_id
        }, source=self)
        
        # 检查是否所有标签页都已关闭
        if self.count() == 0:
            self.all_tabs_closed.emit()
            event_system.emit('all_tabs_closed', source=self)
        
        return True
    
    def _update_tab_indices(self):
        """更新标签页索引映射"""
        new_mapping = {}
        for i in range(self.count()):
            widget = self.widget(i)
            for tab_id, tab_data in self._tabs_data.items():
                if tab_data.widget == widget:
                    new_mapping[tab_id] = i
                    break
        self._tab_id_to_index = new_mapping
    
    def _confirm_close_modified_tab(self, tab_data: TabData) -> bool:
        """确认关闭已修改的标签页"""
        reply = QMessageBox.question(
            self,
            "确认关闭",
            f"标签页 '{tab_data.title}' 有未保存的修改。\n确定要关闭吗？",
            QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
            QMessageBox.Save
        )
        
        if reply == QMessageBox.Save:
            # 尝试保存
            if hasattr(tab_data.widget, 'save'):
                return tab_data.widget.save()
            return True
        elif reply == QMessageBox.Discard:
            return True
        else:
            return False
    
    def activate_tab(self, tab_id: str) -> bool:
        """激活指定标签页"""
        if tab_id not in self._tabs_data:
            return False
        
        index = self._tab_id_to_index[tab_id]
        self.setCurrentIndex(index)
        return True
    
    def get_current_tab_id(self) -> Optional[str]:
        """获取当前激活的标签页ID"""
        current_widget = self.currentWidget()
        if current_widget:
            for tab_id, tab_data in self._tabs_data.items():
                if tab_data.widget == current_widget:
                    return tab_id
        return None
    
    def get_tab_data(self, tab_id: str) -> Optional[TabData]:
        """获取标签页数据"""
        return self._tabs_data.get(tab_id)
    
    def set_tab_modified(self, tab_id: str, modified: bool):
        """设置标签页修改状态"""
        if tab_id not in self._tabs_data:
            return
        
        tab_data = self._tabs_data[tab_id]
        tab_data.modified = modified
        
        # 更新标签页标题显示
        index = self._tab_id_to_index[tab_id]
        title = tab_data.title
        if modified:
            title = "● " + title  # 添加修改标记
        
        self.setTabText(index, title)
    
    def set_tab_title(self, tab_id: str, title: str):
        """设置标签页标题"""
        if tab_id not in self._tabs_data:
            return
        
        tab_data = self._tabs_data[tab_id]
        tab_data.title = title
        
        index = self._tab_id_to_index[tab_id]
        display_title = title
        if tab_data.modified:
            display_title = "● " + title
        
        self.setTabText(index, display_title)
    
    def close_all_tabs(self) -> bool:
        """关闭所有标签页"""
        tab_ids = list(self._tabs_data.keys())
        for tab_id in tab_ids:
            if not self.remove_tab(tab_id):
                return False  # 用户取消了关闭操作
        return True
    
    def close_other_tabs(self, keep_tab_id: str) -> bool:
        """关闭除指定标签页外的所有标签页"""
        tab_ids = [tid for tid in self._tabs_data.keys() if tid != keep_tab_id]
        for tab_id in tab_ids:
            if not self.remove_tab(tab_id):
                return False
        return True

    def _on_current_changed(self, index: int):
        """当前标签页变化处理"""
        if index >= 0:
            widget = self.widget(index)
            for tab_id, tab_data in self._tabs_data.items():
                if tab_data.widget == widget:
                    self.tab_changed.emit(tab_id)
                    event_system.emit('tab_changed', {
                        'tab_id': tab_id,
                        'widget': widget
                    }, source=self)
                    break

    def _on_tab_close_requested(self, index: int):
        """标签页关闭请求处理"""
        widget = self.widget(index)
        for tab_id, tab_data in self._tabs_data.items():
            if tab_data.widget == widget:
                self.tab_close_requested.emit(tab_id)
                self.remove_tab(tab_id)
                break

    def _on_context_menu(self, index: int, global_pos: QPoint):
        """右键菜单处理"""
        widget = self.widget(index)
        tab_id = None

        for tid, tab_data in self._tabs_data.items():
            if tab_data.widget == widget:
                tab_id = tid
                break

        if tab_id:
            self._show_context_menu(tab_id, global_pos)

    def _on_middle_click(self, index: int):
        """中键点击处理"""
        widget = self.widget(index)
        for tab_id, tab_data in self._tabs_data.items():
            if tab_data.widget == widget:
                self.remove_tab(tab_id)
                break

    def _show_context_menu(self, tab_id: str, global_pos: QPoint):
        """显示右键菜单"""
        menu = QMenu(self)

        # 关闭标签页
        close_action = QAction("关闭", self)
        close_action.triggered.connect(lambda: self.remove_tab(tab_id))
        menu.addAction(close_action)

        # 关闭其他标签页
        if len(self._tabs_data) > 1:
            close_others_action = QAction("关闭其他", self)
            close_others_action.triggered.connect(lambda: self.close_other_tabs(tab_id))
            menu.addAction(close_others_action)

        # 关闭所有标签页
        close_all_action = QAction("关闭所有", self)
        close_all_action.triggered.connect(self.close_all_tabs)
        menu.addAction(close_all_action)

        menu.addSeparator()

        # 复制标签页路径（如果有）
        tab_data = self._tabs_data.get(tab_id)
        if tab_data and hasattr(tab_data.widget, 'get_file_path'):
            copy_path_action = QAction("复制路径", self)
            copy_path_action.triggered.connect(
                lambda: self._copy_tab_path(tab_data.widget)
            )
            menu.addAction(copy_path_action)

        menu.exec_(global_pos)

    def _copy_tab_path(self, widget):
        """复制标签页路径到剪贴板"""
        if hasattr(widget, 'get_file_path'):
            from PySide6.QtWidgets import QApplication
            path = widget.get_file_path()
            if path:
                QApplication.clipboard().setText(path)


class Workspace(QWidget):
    """主工作区组件"""

    # 信号
    tab_added = Signal(str, QWidget)
    tab_removed = Signal(str)
    tab_changed = Signal(str)
    workspace_empty = Signal()

    def __init__(self):
        super().__init__()

        self._tab_widget = None
        self._welcome_widget = None
        self._current_layout = None

        # 创建UI
        self._setup_ui()
        self._setup_connections()

    def _setup_ui(self):
        """设置用户界面"""
        # 创建主布局
        self._current_layout = QVBoxLayout(self)
        self._current_layout.setContentsMargins(0, 0, 0, 0)
        self._current_layout.setSpacing(0)

        # 创建标签页组件
        self._tab_widget = TabWidget()
        self._current_layout.addWidget(self._tab_widget)

        # 创建欢迎页面
        self._create_welcome_widget()

        # 初始显示欢迎页面
        self._show_welcome()

    def _create_welcome_widget(self):
        """创建欢迎页面"""
        self._welcome_widget = QWidget()
        layout = QVBoxLayout(self._welcome_widget)
        layout.setAlignment(Qt.AlignCenter)

        # 欢迎标题
        title_label = QLabel("欢迎使用 PySide6 高效软件框架")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #333333;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 描述文本
        desc_label = QLabel("请从侧边栏选择功能开始使用")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666666;
                margin-bottom: 30px;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        # 快速操作按钮
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignCenter)

        # 示例按钮
        new_project_btn = QPushButton("新建项目")
        new_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        buttons_layout.addWidget(new_project_btn)

        open_file_btn = QPushButton("打开文件")
        open_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)
        buttons_layout.addWidget(open_file_btn)

        layout.addLayout(buttons_layout)

        # 连接按钮信号
        new_project_btn.clicked.connect(self._on_new_project)
        open_file_btn.clicked.connect(self._on_open_file)

    def _setup_connections(self):
        """设置信号连接"""
        # 连接标签页组件信号
        self._tab_widget.tab_added.connect(self._on_tab_added)
        self._tab_widget.tab_removed.connect(self._on_tab_removed)
        self._tab_widget.tab_changed.connect(self._on_tab_changed)
        self._tab_widget.all_tabs_closed.connect(self._on_all_tabs_closed)

    def _show_welcome(self):
        """显示欢迎页面"""
        self._tab_widget.hide()
        if self._welcome_widget not in [self._current_layout.itemAt(i).widget()
                                       for i in range(self._current_layout.count())]:
            self._current_layout.addWidget(self._welcome_widget)
        self._welcome_widget.show()

    def _hide_welcome(self):
        """隐藏欢迎页面"""
        self._welcome_widget.hide()
        self._tab_widget.show()

    @exception_handler_decorator()
    def add_tab(self, tab_id: str, widget: QWidget, title: str,
                icon: QIcon = None, closable: bool = True,
                tooltip: str = "", activate: bool = True) -> bool:
        """添加标签页到工作区"""
        # 如果是第一个标签页，隐藏欢迎页面
        if self._tab_widget.count() == 0:
            self._hide_welcome()

        # 添加到标签页组件
        index = self._tab_widget.add_tab(tab_id, widget, title, icon, closable, tooltip, activate)

        return index >= 0

    def remove_tab(self, tab_id: str) -> bool:
        """从工作区移除标签页"""
        return self._tab_widget.remove_tab(tab_id)

    def activate_tab(self, tab_id: str) -> bool:
        """激活指定标签页"""
        return self._tab_widget.activate_tab(tab_id)

    def get_current_tab_id(self) -> Optional[str]:
        """获取当前激活的标签页ID"""
        return self._tab_widget.get_current_tab_id()

    def get_tab_widget(self) -> TabWidget:
        """获取标签页组件"""
        return self._tab_widget

    def set_tab_modified(self, tab_id: str, modified: bool):
        """设置标签页修改状态"""
        self._tab_widget.set_tab_modified(tab_id, modified)

    def set_tab_title(self, tab_id: str, title: str):
        """设置标签页标题"""
        self._tab_widget.set_tab_title(tab_id, title)

    def close_all_tabs(self) -> bool:
        """关闭所有标签页"""
        return self._tab_widget.close_all_tabs()

    def _on_tab_added(self, tab_id: str, widget: QWidget):
        """标签页添加处理"""
        self.tab_added.emit(tab_id, widget)

    def _on_tab_removed(self, tab_id: str):
        """标签页移除处理"""
        self.tab_removed.emit(tab_id)

    def _on_tab_changed(self, tab_id: str):
        """标签页切换处理"""
        self.tab_changed.emit(tab_id)

    def _on_all_tabs_closed(self):
        """所有标签页关闭处理"""
        self._show_welcome()
        self.workspace_empty.emit()
        event_system.emit('workspace_empty', source=self)

    def _on_new_project(self):
        """新建项目处理"""
        event_system.emit('new_project_requested', source=self)

    def _on_open_file(self):
        """打开文件处理"""
        event_system.emit('open_file_requested', source=self)
