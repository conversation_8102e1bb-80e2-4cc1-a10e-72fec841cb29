"""
配置管理器 - 集中化配置管理
支持动态配置加载和热更新
"""
import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class Config<PERSON><PERSON><PERSON><PERSON><PERSON>ler(FileSystemEventHandler):
    """配置文件变化监听器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.yaml'):
            self.config_manager.reload_config()


class ConfigManager:
    """配置管理器 - 单例模式"""
    
    _instance = None
    _config = None
    _config_path = None
    _observer = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._config = {}
            self._load_default_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        config_path = Path("config/app_config.yaml")
        if config_path.exists():
            self.load_config(config_path)
        else:
            # 默认配置
            self._config = {
                "app": {"name": "PySide6 Framework", "version": "1.0.0"},
                "window": {"width": 1200, "height": 800},
                "sidebar": {"icon_bar_width": 50, "content_min_width": 125}
            }
    
    def load_config(self, config_path: str | Path):
        """加载配置文件"""
        self._config_path = Path(config_path)
        try:
            with open(self._config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
            self._start_watching()
        except Exception as e:
            print(f"配置文件加载失败: {e}")
    
    def _start_watching(self):
        """开始监听配置文件变化"""
        if self._observer:
            self._observer.stop()
        
        self._observer = Observer()
        handler = ConfigChangeHandler(self)
        self._observer.schedule(handler, str(self._config_path.parent), recursive=False)
        self._observer.start()
    
    def reload_config(self):
        """重新加载配置"""
        if self._config_path and self._config_path.exists():
            try:
                with open(self._config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
                print("配置文件已重新加载")
            except Exception as e:
                print(f"配置文件重新加载失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def save_config(self, config_path: Optional[str | Path] = None):
        """保存配置到文件"""
        path = Path(config_path) if config_path else self._config_path
        if path:
            try:
                path.parent.mkdir(parents=True, exist_ok=True)
                with open(path, 'w', encoding='utf-8') as f:
                    yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
            except Exception as e:
                print(f"配置文件保存失败: {e}")
    
    def stop_watching(self):
        """停止监听配置文件变化"""
        if self._observer:
            self._observer.stop()
            self._observer.join()


# 全局配置管理器实例
config = ConfigManager()
