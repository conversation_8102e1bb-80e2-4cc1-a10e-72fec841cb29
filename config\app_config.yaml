# 应用程序配置文件
app:
  name: "PySide6 Framework"
  version: "1.0.0"
  debug: true
  
# 窗口配置
window:
  title: "PySide6 高效软件框架"
  width: 1200
  height: 800
  min_width: 800
  min_height: 600
  
# 侧边栏配置
sidebar:
  icon_bar_width: 50
  content_min_width: 125  # 2.5倍图标栏宽度
  default_width: 300
  collapsible: true
  
# 插件配置
plugins:
  directory: "plugins"
  auto_load: true
  manifest_file: "plugin.json"
  
# 主题配置
theme:
  default: "light"
  available: ["light", "dark"]
  
# 日志配置
logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5
