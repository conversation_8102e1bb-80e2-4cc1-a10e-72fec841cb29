"""
侧边栏系统 - 可折叠的侧边栏，包括图标栏和内容栏
"""
from typing import Dict, List, Optional, Any
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton,
                               QStackedWidget, QLabel, QFrame, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect, QTimer, QPoint
from PySide6.QtGui import QIcon, QPainter, QPixmap, QFont, QMouseEvent, QCursor

from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import exception_handler_decorator


class IconButton(QPushButton):
    """侧边栏图标按钮"""
    
    def __init__(self, icon_path: str = None, text: str = "", tooltip: str = ""):
        super().__init__()
        
        self._is_active = False
        self._icon_path = icon_path
        
        # 设置按钮属性
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip or text)
        self.setText(text[:1].upper() if not icon_path else "")
        
        # 设置样式
        self._setup_style()
        
        # 如果有图标路径，加载图标
        if icon_path:
            self._load_icon(icon_path)
    
    def _setup_style(self):
        """设置按钮样式"""
        self.setStyleSheet("""
            IconButton {
                border: none;
                border-radius: 6px;
                background-color: transparent;
                color: #666666;
                font-weight: bold;
                font-size: 14px;
            }
            
            IconButton:hover {
                background-color: #e0e0e0;
                color: #333333;
            }
            
            IconButton:pressed {
                background-color: #d0d0d0;
            }
        """)
    
    def _load_icon(self, icon_path: str):
        """加载图标"""
        try:
            icon = QIcon(icon_path)
            self.setIcon(icon)
            self.setText("")  # 有图标时清除文字
        except Exception:
            # 图标加载失败时保持文字
            pass
    
    def set_active(self, active: bool):
        """设置按钮激活状态"""
        self._is_active = active
        self._update_style()
    
    def _update_style(self):
        """更新样式"""
        if self._is_active:
            self.setStyleSheet("""
                IconButton {
                    border: none;
                    border-radius: 6px;
                    background-color: #007acc;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                }
                
                IconButton:hover {
                    background-color: #005a9e;
                }
            """)
        else:
            self._setup_style()
    
    def is_active(self) -> bool:
        """检查是否激活"""
        return self._is_active


class IconBar(QWidget):
    """图标栏 - 侧边栏左侧的图标按钮栏"""
    
    # 信号
    icon_clicked = Signal(str)  # 图标ID
    
    def __init__(self):
        super().__init__()
        
        self._buttons: Dict[str, IconButton] = {}
        self._active_button: Optional[str] = None
        
        # 设置固定宽度
        icon_width = config.get('sidebar.icon_bar_width', 50)
        self.setFixedWidth(icon_width)
        self.setMinimumHeight(200)
        
        # 创建布局
        self._setup_ui()
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 10, 5, 10)
        layout.setSpacing(5)
        layout.setAlignment(Qt.AlignTop)
        
        # 设置背景样式
        self.setStyleSheet("""
            IconBar {
                background-color: #f8f8f8;
                border-right: 1px solid #e0e0e0;
            }
        """)
    
    @exception_handler_decorator()
    def add_icon(self, icon_id: str, icon_path: str = None, 
                 text: str = "", tooltip: str = ""):
        """添加图标按钮"""
        if icon_id in self._buttons:
            return  # 已存在
        
        # 创建按钮
        button = IconButton(icon_path, text, tooltip)
        button.clicked.connect(lambda: self._on_icon_clicked(icon_id))
        
        # 添加到布局
        self.layout().addWidget(button)
        
        # 保存引用
        self._buttons[icon_id] = button
        
        # 如果是第一个按钮，设为激活
        if len(self._buttons) == 1:
            self.set_active_icon(icon_id)
    
    def remove_icon(self, icon_id: str):
        """移除图标按钮"""
        if icon_id not in self._buttons:
            return
        
        button = self._buttons[icon_id]
        
        # 从布局移除
        self.layout().removeWidget(button)
        button.deleteLater()
        
        # 从字典移除
        del self._buttons[icon_id]
        
        # 如果移除的是激活按钮，激活第一个可用按钮
        if self._active_button == icon_id:
            self._active_button = None
            if self._buttons:
                first_id = next(iter(self._buttons))
                self.set_active_icon(first_id)
    
    def set_active_icon(self, icon_id: str):
        """设置激活的图标"""
        if icon_id not in self._buttons:
            return
        
        # 取消之前的激活状态
        if self._active_button and self._active_button in self._buttons:
            self._buttons[self._active_button].set_active(False)
        
        # 设置新的激活状态
        self._active_button = icon_id
        self._buttons[icon_id].set_active(True)
    
    def get_active_icon(self) -> Optional[str]:
        """获取当前激活的图标ID"""
        return self._active_button
    
    def _on_icon_clicked(self, icon_id: str):
        """图标点击处理"""
        # 如果点击的是当前激活的图标，发送折叠信号
        if self._active_button == icon_id:
            self.icon_clicked.emit(icon_id)
        else:
            # 否则切换到新图标
            self.set_active_icon(icon_id)
            self.icon_clicked.emit(icon_id)


class ContentPanel(QStackedWidget):
    """内容面板 - 侧边栏右侧的内容区域"""
    
    def __init__(self):
        super().__init__()
        
        self._panels: Dict[str, QWidget] = {}
        
        # 设置最小宽度
        min_width = config.get('sidebar.content_min_width', 125)
        self.setMinimumWidth(min_width)
        
        # 设置样式
        self.setStyleSheet("""
            ContentPanel {
                background-color: #ffffff;
                border: none;
            }
        """)
    
    @exception_handler_decorator()
    def add_panel(self, panel_id: str, widget: QWidget):
        """添加内容面板"""
        if panel_id in self._panels:
            return  # 已存在
        
        # 添加到堆叠组件
        self.addWidget(widget)
        
        # 保存引用
        self._panels[panel_id] = widget
    
    def remove_panel(self, panel_id: str):
        """移除内容面板"""
        if panel_id not in self._panels:
            return
        
        widget = self._panels[panel_id]
        
        # 从堆叠组件移除
        self.removeWidget(widget)
        widget.deleteLater()
        
        # 从字典移除
        del self._panels[panel_id]
    
    def show_panel(self, panel_id: str):
        """显示指定面板"""
        if panel_id in self._panels:
            widget = self._panels[panel_id]
            self.setCurrentWidget(widget)
    
    def get_current_panel_id(self) -> Optional[str]:
        """获取当前显示的面板ID"""
        current_widget = self.currentWidget()
        for panel_id, widget in self._panels.items():
            if widget == current_widget:
                return panel_id
        return None


class Sidebar(QWidget):
    """侧边栏主组件"""

    # 信号
    panel_changed = Signal(str)  # 面板ID
    collapsed = Signal()
    expanded = Signal()
    width_changed = Signal(int)  # 宽度变化信号

    def __init__(self):
        super().__init__()

        self._icon_bar = None
        self._content_panel = None
        self._is_collapsed = False
        self._animation = None
        self._original_width = 0

        # 拖拽相关
        self._is_resizing = False
        self._resize_start_pos = QPoint()
        self._resize_start_width = 0
        self._resize_margin = 5  # 拖拽区域边距

        # 设置默认宽度
        default_width = config.get('sidebar.default_width', 300)
        self.setFixedWidth(default_width)
        self._original_width = default_width

        # 创建UI
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """设置用户界面"""
        # 创建水平布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建图标栏
        self._icon_bar = IconBar()
        layout.addWidget(self._icon_bar)
        
        # 创建内容面板
        self._content_panel = ContentPanel()
        layout.addWidget(self._content_panel)
        
        # 设置拉伸因子
        layout.setStretchFactor(self._icon_bar, 0)  # 固定宽度
        layout.setStretchFactor(self._content_panel, 1)  # 动态宽度
    
    def _setup_connections(self):
        """设置信号连接"""
        self._icon_bar.icon_clicked.connect(self._on_icon_clicked)
    
    def _on_icon_clicked(self, icon_id: str):
        """图标点击处理"""
        current_active = self._icon_bar.get_active_icon()
        
        if current_active == icon_id and not self._is_collapsed:
            # 点击当前激活图标且未折叠 -> 折叠
            self.collapse()
        else:
            # 切换面板或展开
            if self._is_collapsed:
                self.expand()
            
            # 显示对应面板
            self._content_panel.show_panel(icon_id)
            self.panel_changed.emit(icon_id)
    
    @exception_handler_decorator()
    def add_panel(self, panel_id: str, widget: QWidget, 
                  icon_path: str = None, text: str = "", tooltip: str = ""):
        """添加侧边栏面板"""
        # 添加图标
        self._icon_bar.add_icon(panel_id, icon_path, text, tooltip)
        
        # 添加内容面板
        self._content_panel.add_panel(panel_id, widget)
        
        # 发布面板添加事件
        event_system.emit('sidebar_panel_added', {
            'panel_id': panel_id,
            'widget': widget
        }, source=self)
    
    def remove_panel(self, panel_id: str):
        """移除侧边栏面板"""
        # 移除图标
        self._icon_bar.remove_icon(panel_id)
        
        # 移除内容面板
        self._content_panel.remove_panel(panel_id)
        
        # 发布面板移除事件
        event_system.emit('sidebar_panel_removed', {
            'panel_id': panel_id
        }, source=self)
    
    def collapse(self):
        """折叠侧边栏"""
        if self._is_collapsed:
            return
        
        self._is_collapsed = True
        
        # 创建动画
        self._animate_width(self.width(), self._icon_bar.width())
        
        # 发布折叠事件
        self.collapsed.emit()
        event_system.emit('sidebar_collapsed', source=self)
    
    def expand(self):
        """展开侧边栏"""
        if not self._is_collapsed:
            return
        
        self._is_collapsed = False
        
        # 创建动画
        self._animate_width(self.width(), self._original_width)
        
        # 发布展开事件
        self.expanded.emit()
        event_system.emit('sidebar_expanded', source=self)
    
    def _animate_width(self, start_width: int, end_width: int):
        """宽度动画"""
        if self._animation:
            self._animation.stop()
        
        self._animation = QPropertyAnimation(self, b"maximumWidth")
        self._animation.setDuration(200)
        self._animation.setStartValue(start_width)
        self._animation.setEndValue(end_width)
        self._animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 动画完成后的处理
        def on_finished():
            self.setFixedWidth(end_width)
            if self._is_collapsed:
                self._content_panel.hide()
            else:
                self._content_panel.show()
        
        self._animation.finished.connect(on_finished)
        self._animation.start()
    
    def toggle_collapse(self):
        """切换折叠状态"""
        if self._is_collapsed:
            self.expand()
        else:
            self.collapse()
    
    def is_collapsed(self) -> bool:
        """检查是否已折叠"""
        return self._is_collapsed
    
    def get_current_panel(self) -> Optional[str]:
        """获取当前显示的面板ID"""
        return self._content_panel.get_current_panel_id()
    
    def set_panel_visible(self, panel_id: str, visible: bool):
        """设置面板可见性"""
        if visible:
            self._content_panel.show_panel(panel_id)
            self._icon_bar.set_active_icon(panel_id)
        # 隐藏面板的逻辑可以根据需要实现

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查是否在右边缘拖拽区域
            if self._is_in_resize_area(event.pos()):
                self._is_resizing = True
                self._resize_start_pos = event.globalPos()
                self._resize_start_width = self.width()
                self.setCursor(QCursor(Qt.SizeHorCursor))
                event.accept()
                return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self._is_resizing:
            # 计算新宽度
            delta = event.globalPos().x() - self._resize_start_pos.x()
            new_width = self._resize_start_width + delta

            # 限制最小和最大宽度
            min_width = self._icon_bar.width() + config.get('sidebar.content_min_width', 125)
            max_width = 600  # 最大宽度限制

            new_width = max(min_width, min(new_width, max_width))

            # 检查是否需要折叠
            if new_width <= min_width + 10:  # 10px容差
                if not self._is_collapsed:
                    self.collapse()
            else:
                # 更新宽度
                if self._is_collapsed:
                    self.expand()

                self.setFixedWidth(new_width)
                self._original_width = new_width
                self.width_changed.emit(new_width)

            event.accept()
            return

        # 检查鼠标是否在拖拽区域，更新光标
        if self._is_in_resize_area(event.pos()):
            self.setCursor(QCursor(Qt.SizeHorCursor))
        else:
            self.setCursor(QCursor(Qt.ArrowCursor))

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if self._is_resizing:
            self._is_resizing = False
            self.setCursor(QCursor(Qt.ArrowCursor))

            # 发布宽度变化事件
            event_system.emit('sidebar_width_changed', {
                'width': self.width(),
                'collapsed': self._is_collapsed
            }, source=self)

            event.accept()
            return

        super().mouseReleaseEvent(event)

    def _is_in_resize_area(self, pos: QPoint) -> bool:
        """检查位置是否在拖拽调整区域"""
        if self._is_collapsed:
            return False

        # 检查是否在右边缘附近
        right_edge = self.width()
        return abs(pos.x() - right_edge) <= self._resize_margin

    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self._is_resizing:
            self.setCursor(QCursor(Qt.ArrowCursor))
        super().leaveEvent(event)
