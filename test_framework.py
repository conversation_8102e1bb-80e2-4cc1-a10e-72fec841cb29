"""
框架测试脚本 - 验证框架的基本功能
"""
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_config_manager():
    """测试配置管理器"""
    print("测试配置管理器...")
    
    from core.config_manager import config
    
    # 测试基本配置获取
    app_name = config.get('app.name', 'Default App')
    print(f"应用名称: {app_name}")
    
    # 测试配置设置
    config.set('test.value', 'test_data')
    test_value = config.get('test.value')
    assert test_value == 'test_data', "配置设置/获取失败"
    
    print("✓ 配置管理器测试通过")


def test_event_system():
    """测试事件系统"""
    print("测试事件系统...")
    
    from core.event_system import event_system
    
    # 测试事件订阅和发布
    received_data = []
    
    def test_handler(event):
        received_data.append(event.data)
    
    # 订阅事件
    handler = event_system.subscribe('test_event', test_handler)
    
    # 发布事件
    event_system.emit('test_event', 'test_data', immediate=True)
    
    # 验证事件接收
    assert len(received_data) == 1, "事件未正确接收"
    assert received_data[0] == 'test_data', "事件数据不正确"
    
    # 取消订阅
    event_system.unsubscribe('test_event', handler)
    
    print("✓ 事件系统测试通过")


def test_exception_handler():
    """测试异常处理器"""
    print("测试异常处理器...")

    from core.exception_handler import global_exception_handler

    # 测试安全调用
    def test_function():
        return "success"

    def error_function():
        raise ValueError("测试异常")

    # 测试正常调用
    result = global_exception_handler.safe_call(test_function)
    assert result == "success", "安全调用正常函数失败"

    # 测试异常调用
    result = global_exception_handler.safe_call(error_function)
    assert result is None, "安全调用异常函数应返回None"

    print("✓ 异常处理器测试通过")


def test_plugin_interface():
    """测试插件接口"""
    print("测试插件接口...")
    
    from plugins.plugin_interface import PluginManifest, PluginContext, IPlugin
    from plugins.plugin_manager import plugin_manager
    
    # 测试插件清单
    manifest_data = {
        'id': 'test_plugin',
        'name': 'Test Plugin',
        'version': '1.0.0',
        'description': 'A test plugin',
        'author': 'Test Author',
        'main': 'main.py'
    }
    
    manifest = PluginManifest(**manifest_data)
    assert manifest.id == 'test_plugin', "插件清单创建失败"
    
    # 测试插件上下文
    context = PluginContext(plugin_manager, manifest)
    assert context.manifest.id == 'test_plugin', "插件上下文创建失败"
    
    print("✓ 插件接口测试通过")


def test_ui_components():
    """测试UI组件（需要Qt环境）"""
    print("测试UI组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.sidebar import Sidebar, IconButton
        from ui.workspace import Workspace, TabWidget
        
        # 创建Qt应用程序（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 测试侧边栏组件
        sidebar = Sidebar()
        assert sidebar is not None, "侧边栏创建失败"
        
        # 测试工作区组件
        workspace = Workspace()
        assert workspace is not None, "工作区创建失败"
        
        # 测试图标按钮
        icon_button = IconButton(text="T", tooltip="Test Button")
        assert icon_button is not None, "图标按钮创建失败"
        
        print("✓ UI组件测试通过")
        
    except ImportError as e:
        print(f"⚠ UI组件测试跳过（缺少PySide6）: {e}")


def test_directory_structure():
    """测试目录结构"""
    print("测试目录结构...")
    
    required_dirs = [
        "src/core",
        "src/ui", 
        "src/plugins",
        "config",
        "plugins"
    ]
    
    required_files = [
        "src/core/__init__.py",
        "src/core/config_manager.py",
        "src/core/event_system.py",
        "src/core/exception_handler.py",
        "src/core/application.py",
        "src/ui/__init__.py",
        "src/ui/main_window.py",
        "src/ui/layout_manager.py",
        "src/ui/sidebar.py",
        "src/ui/workspace.py",
        "src/plugins/__init__.py",
        "src/plugins/plugin_interface.py",
        "src/plugins/plugin_manager.py",
        "config/app_config.yaml",
        "requirements.txt"
    ]
    
    # 检查目录
    for dir_path in required_dirs:
        path = Path(dir_path)
        assert path.exists() and path.is_dir(), f"目录不存在: {dir_path}"
    
    # 检查文件
    for file_path in required_files:
        path = Path(file_path)
        assert path.exists() and path.is_file(), f"文件不存在: {file_path}"
    
    print("✓ 目录结构测试通过")


def test_plugin_example():
    """测试示例插件"""
    print("测试示例插件...")
    
    plugin_manifest = Path("plugins/project_manager/plugin.json")
    plugin_main = Path("plugins/project_manager/main.py")
    
    assert plugin_manifest.exists(), "示例插件清单文件不存在"
    assert plugin_main.exists(), "示例插件主文件不存在"
    
    # 验证插件清单格式
    import json
    with open(plugin_manifest, 'r', encoding='utf-8') as f:
        manifest_data = json.load(f)
    
    required_fields = ['id', 'name', 'version', 'description', 'author', 'main']
    for field in required_fields:
        assert field in manifest_data, f"插件清单缺少字段: {field}"
    
    print("✓ 示例插件测试通过")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始运行框架测试")
    print("=" * 50)
    
    tests = [
        test_directory_structure,
        test_config_manager,
        test_event_system,
        test_exception_handler,
        test_plugin_interface,
        test_plugin_example,
        test_ui_components,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} 失败: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 所有测试通过！框架基本功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关组件。")
        return False


def create_demo_project():
    """创建演示项目"""
    print("创建演示项目...")
    
    demo_dir = Path("demo_project")
    demo_dir.mkdir(exist_ok=True)
    
    # 创建项目文件
    project_data = {
        "name": "演示项目",
        "version": "1.0.0",
        "description": "这是一个演示项目，用于测试框架功能",
        "created": "2024-01-01",
        "files": []
    }
    
    import json
    with open(demo_dir / "demo.project", 'w', encoding='utf-8') as f:
        json.dump(project_data, f, indent=2, ensure_ascii=False)
    
    # 创建一些示例文件
    (demo_dir / "README.md").write_text("""# 演示项目

这是一个演示项目，用于测试PySide6高效软件框架的功能。

## 功能特性

- 插件系统
- 侧边栏管理
- 标签页工作区
- 配置管理
- 事件系统
- 异常处理

## 使用方法

1. 运行 `python myapp/main.py` 启动框架
2. 在侧边栏中选择"项目"面板
3. 点击"打开"按钮选择此项目文件
4. 双击文件在工作区中打开

""", encoding='utf-8')
    
    (demo_dir / "sample.txt").write_text("""这是一个示例文本文件。

你可以在项目管理器中双击此文件来在工作区中打开它。

框架支持：
- 多标签页编辑
- 文件浏览
- 插件扩展
- 配置管理
""", encoding='utf-8')
    
    print(f"✓ 演示项目已创建在: {demo_dir.absolute()}")


if __name__ == "__main__":
    # 运行测试
    success = run_all_tests()
    
    if success:
        # 创建演示项目
        create_demo_project()
        
        print("\n" + "=" * 50)
        print("框架准备就绪！")
        print("=" * 50)
        print("运行命令启动框架:")
        print("  python myapp/main.py")
        print()
        print("或者运行以下命令安装依赖:")
        print("  pip install -r requirements.txt")
        print("  python myapp/main.py")
    
    sys.exit(0 if success else 1)
