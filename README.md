# PySide6 高效软件框架

一个基于PySide6的高效软件框架，采用插件系统进行功能实现和扩展，界面简洁直观，支持多分辨率屏幕和动态布局调整。

## 🚀 核心特性

### 🎯 核心目标
- **插件系统**: 仿照VSCode的插件架构，支持功能的动态扩展
- **响应式布局**: 适配多分辨率屏幕，支持用户动态调整布局
- **高效架构**: 分层设计，高内聚低耦合，模块化组件化

### 🏗️ 整体布局
```
+-------------------------+--------------------------------+
|   左侧侧边栏 (25%)      |     中间工作区 (动态50%)        |
|   - 项目管理            |   - 标签页管理                  |
|   - 数据视图列表        |   - 多文档编辑                  |
+-------------------------+--------------------------------+
```

### 📋 侧边栏特性
- **双栏布局**: 图标栏(50px) + 内容栏(动态宽度)
- **智能折叠**: 点击当前图标折叠，点击其他图标切换
- **拖拽调整**: 支持鼠标拖拽调整宽度，自动折叠机制
- **响应式**: 内容栏最小宽度为图标栏的2.5倍

## 📁 项目结构

```
myapp/
├── src/                          # 源代码目录
│   ├── core/                     # 核心模块
│   │   ├── __init__.py
│   │   ├── application.py        # 主应用程序类
│   │   ├── config_manager.py     # 配置管理器
│   │   ├── event_system.py       # 事件系统
│   │   └── exception_handler.py  # 异常处理器
│   ├── ui/                       # 用户界面模块
│   │   ├── __init__.py
│   │   ├── main_window.py        # 主窗口
│   │   ├── layout_manager.py     # 布局管理器
│   │   ├── sidebar.py            # 侧边栏系统
│   │   └── workspace.py          # 工作区和标签页
│   └── plugins/                  # 插件模块
│       ├── __init__.py
│       ├── plugin_interface.py   # 插件接口定义
│       └── plugin_manager.py     # 插件管理器
├── config/                       # 配置文件
│   └── app_config.yaml          # 应用配置
├── plugins/                      # 插件目录
│   └── project_manager/          # 示例插件
│       ├── plugin.json          # 插件清单
│       └── main.py              # 插件主文件
├── myapp/                        # 应用程序入口
│   └── main.py                  # 主程序
├── requirements.txt              # 依赖包列表
├── test_framework.py            # 测试脚本
└── README.md                    # 项目说明
```

## 🛠️ 安装和运行

### 环境要求
- Python 3.8+
- PySide6 6.5.0+

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python myapp/main.py
```

### 运行测试
```bash
python test_framework.py
```

## 🔌 插件系统

### 插件架构
框架采用类似VSCode的插件系统，支持：
- **动态加载**: 运行时发现和加载插件
- **生命周期管理**: 插件的激活、停用和卸载
- **API接口**: 丰富的插件API，支持UI扩展和功能注册
- **事件通信**: 基于事件系统的插件间通信

### 创建插件

1. **创建插件目录**
```
plugins/my_plugin/
├── plugin.json    # 插件清单
└── main.py       # 插件主文件
```

2. **编写插件清单** (`plugin.json`)
```json
{
  "id": "my_plugin",
  "name": "我的插件",
  "version": "1.0.0",
  "description": "插件描述",
  "author": "作者名",
  "main": "main.py",
  "activation_events": ["*"]
}
```

3. **实现插件类** (`main.py`)
```python
from plugins.plugin_interface import IPlugin

class MyPlugin(IPlugin):
    def activate(self, context):
        # 插件激活逻辑
        context.register_sidebar_panel(
            "my_panel", 
            MyWidget(), 
            "我的面板"
        )
    
    def deactivate(self):
        # 插件停用逻辑
        pass
```

### 插件API

插件可以使用以下API：
- `context.register_command()` - 注册命令
- `context.register_sidebar_panel()` - 注册侧边栏面板
- `context.register_menu_item()` - 注册菜单项
- `context.subscribe_event()` - 订阅事件
- `context.emit_event()` - 发布事件
- `context.get_config()` - 获取配置
- `context.show_message()` - 显示消息

## ⚙️ 配置管理

### 配置文件
主配置文件位于 `config/app_config.yaml`，支持：
- **热更新**: 配置文件变化时自动重新加载
- **分层配置**: 应用配置、插件配置分离
- **类型安全**: 支持多种数据类型

### 配置示例
```yaml
app:
  name: "PySide6 Framework"
  version: "1.0.0"

window:
  width: 1200
  height: 800

sidebar:
  icon_bar_width: 50
  default_width: 300

plugins:
  auto_load: true
```

## 🎨 界面特性

### 主题支持
- 浅色主题（默认）
- 深色主题
- 自定义主题扩展

### 响应式设计
- 多分辨率适配
- 动态布局调整
- 组件自适应缩放

### 交互体验
- 平滑动画效果
- 拖拽操作支持
- 键盘快捷键
- 右键菜单

## 🔧 开发指南

### 架构原则
- **分层架构**: 核心层、UI层、插件层清晰分离
- **事件驱动**: 基于事件系统的松耦合通信
- **可扩展性**: 插件化架构支持功能扩展
- **可观测性**: 完整的日志和异常处理

### 扩展框架

1. **添加新的UI组件**
   - 继承基础组件类
   - 实现标准接口
   - 注册到布局管理器

2. **扩展事件系统**
   - 定义新的事件类型
   - 实现事件处理器
   - 发布和订阅事件

3. **添加配置选项**
   - 在配置文件中定义
   - 使用配置管理器访问
   - 支持热更新

## 🧪 测试

框架包含完整的测试套件：
- 核心模块测试
- UI组件测试
- 插件系统测试
- 集成测试

运行测试：
```bash
python test_framework.py
```

## 📝 示例

### 快速开始
1. 运行框架：`python myapp/main.py`
2. 在侧边栏点击"项目"图标
3. 点击"打开"按钮，选择 `demo_project/demo.project`
4. 双击文件在工作区中打开

### 开发插件
参考 `plugins/project_manager/` 目录下的示例插件实现。

## 🤝 贡献

欢迎贡献代码、报告问题或提出建议！

## 📄 许可证

MIT License

## 🔗 相关链接

- [PySide6 官方文档](https://doc.qt.io/qtforpython/)
- [Python 官方网站](https://www.python.org/)

---

**PySide6 高效软件框架** - 让桌面应用开发更简单、更高效！
