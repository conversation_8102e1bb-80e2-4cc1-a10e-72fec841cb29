"""
插件接口定义 - 定义插件系统的基础接口和抽象类
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QWidget, QMenu
from PySide6.QtGui import QAction


class PluginState(Enum):
    """插件状态枚举"""
    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginManifest:
    """插件清单数据类"""
    id: str
    name: str
    version: str
    description: str
    author: str
    main: str  # 主入口文件
    
    # 可选字段
    display_name: Optional[str] = None
    icon: Optional[str] = None
    category: Optional[str] = None
    keywords: List[str] = None
    homepage: Optional[str] = None
    repository: Optional[str] = None
    license: Optional[str] = None
    
    # 依赖和兼容性
    dependencies: Dict[str, str] = None  # 插件依赖
    engine_version: Optional[str] = None  # 框架版本要求
    
    # 激活事件
    activation_events: List[str] = None
    
    # 贡献点
    contributes: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.dependencies is None:
            self.dependencies = {}
        if self.activation_events is None:
            self.activation_events = ["*"]  # 默认总是激活
        if self.contributes is None:
            self.contributes = {}
        if self.display_name is None:
            self.display_name = self.name


class PluginContext:
    """插件上下文 - 提供给插件的API接口"""
    
    def __init__(self, plugin_manager, manifest: PluginManifest):
        self._plugin_manager = plugin_manager
        self._manifest = manifest
        self._subscriptions: List[Callable] = []
        self._disposables: List[Any] = []
    
    @property
    def manifest(self) -> PluginManifest:
        """获取插件清单"""
        return self._manifest
    
    @property
    def plugin_path(self) -> str:
        """获取插件路径"""
        return self._plugin_manager.get_plugin_path(self._manifest.id)
    
    def subscribe_event(self, event_name: str, callback: Callable):
        """订阅事件"""
        from core.event_system import event_system
        handler = event_system.subscribe(event_name, callback)
        self._subscriptions.append(handler)
        return handler

    def emit_event(self, event_name: str, data: Any = None):
        """发布事件"""
        from core.event_system import event_system
        event_system.emit(event_name, data, source=self._manifest.id)
    
    def register_command(self, command_id: str, callback: Callable, 
                        title: str = "", description: str = ""):
        """注册命令"""
        return self._plugin_manager.register_command(
            self._manifest.id, command_id, callback, title, description
        )
    
    def register_menu_item(self, menu_path: str, command_id: str, 
                          title: str = "", icon: str = None):
        """注册菜单项"""
        return self._plugin_manager.register_menu_item(
            self._manifest.id, menu_path, command_id, title, icon
        )
    
    def register_sidebar_panel(self, panel_id: str, widget: QWidget, 
                              title: str = "", icon: str = None):
        """注册侧边栏面板"""
        return self._plugin_manager.register_sidebar_panel(
            self._manifest.id, panel_id, widget, title, icon
        )
    
    def register_workspace_provider(self, provider_id: str, provider):
        """注册工作区提供者"""
        return self._plugin_manager.register_workspace_provider(
            self._manifest.id, provider_id, provider
        )
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取插件配置"""
        from core.config_manager import config
        plugin_config_key = f"plugins.{self._manifest.id}.{key}"
        return config.get(plugin_config_key, default)

    def set_config(self, key: str, value: Any):
        """设置插件配置"""
        from core.config_manager import config
        plugin_config_key = f"plugins.{self._manifest.id}.{key}"
        config.set(plugin_config_key, value)
    
    def show_message(self, message: str, level: str = "info"):
        """显示消息"""
        self._plugin_manager.show_message(message, level, self._manifest.id)
    
    def add_disposable(self, disposable: Any):
        """添加可释放资源"""
        self._disposables.append(disposable)
    
    def dispose(self):
        """释放资源"""
        # 取消事件订阅
        from core.event_system import event_system
        for handler in self._subscriptions:
            # 这里需要实现取消订阅的逻辑
            pass
        
        # 释放其他资源
        for disposable in self._disposables:
            if hasattr(disposable, 'dispose'):
                disposable.dispose()
            elif hasattr(disposable, 'deleteLater'):
                disposable.deleteLater()
        
        self._subscriptions.clear()
        self._disposables.clear()


class IPlugin(ABC):
    """插件接口抽象类"""
    
    def __init__(self):
        self._context: Optional[PluginContext] = None
        self._state = PluginState.INACTIVE
    
    @property
    def context(self) -> Optional[PluginContext]:
        """获取插件上下文"""
        return self._context
    
    @property
    def state(self) -> PluginState:
        """获取插件状态"""
        return self._state
    
    def _set_context(self, context: PluginContext):
        """设置插件上下文（由插件管理器调用）"""
        self._context = context
    
    def _set_state(self, state: PluginState):
        """设置插件状态（由插件管理器调用）"""
        self._state = state
    
    @abstractmethod
    def activate(self, context: PluginContext):
        """激活插件"""
        pass
    
    @abstractmethod
    def deactivate(self):
        """停用插件"""
        pass
    
    def on_config_changed(self, key: str, value: Any):
        """配置变化回调"""
        pass
    
    def on_event(self, event_name: str, data: Any):
        """事件回调"""
        pass


class PluginCommand:
    """插件命令类"""
    
    def __init__(self, plugin_id: str, command_id: str, callback: Callable,
                 title: str = "", description: str = ""):
        self.plugin_id = plugin_id
        self.command_id = command_id
        self.callback = callback
        self.title = title or command_id
        self.description = description
        self.full_id = f"{plugin_id}.{command_id}"
    
    def execute(self, *args, **kwargs):
        """执行命令"""
        return self.callback(*args, **kwargs)


class PluginMenuItem:
    """插件菜单项类"""
    
    def __init__(self, plugin_id: str, menu_path: str, command_id: str,
                 title: str = "", icon: str = None):
        self.plugin_id = plugin_id
        self.menu_path = menu_path
        self.command_id = command_id
        self.title = title
        self.icon = icon
        self.action: Optional[QAction] = None


class PluginContribution:
    """插件贡献点基类"""
    
    def __init__(self, plugin_id: str, contribution_type: str):
        self.plugin_id = plugin_id
        self.contribution_type = contribution_type
        self.disposed = False
    
    def dispose(self):
        """释放贡献点资源"""
        self.disposed = True


class SidebarPanelContribution(PluginContribution):
    """侧边栏面板贡献点"""
    
    def __init__(self, plugin_id: str, panel_id: str, widget: QWidget,
                 title: str = "", icon: str = None):
        super().__init__(plugin_id, "sidebar_panel")
        self.panel_id = panel_id
        self.widget = widget
        self.title = title
        self.icon = icon
    
    def dispose(self):
        super().dispose()
        if self.widget and not self.widget.isHidden():
            self.widget.hide()
            self.widget.deleteLater()


class WorkspaceProviderContribution(PluginContribution):
    """工作区提供者贡献点"""
    
    def __init__(self, plugin_id: str, provider_id: str, provider):
        super().__init__(plugin_id, "workspace_provider")
        self.provider_id = provider_id
        self.provider = provider
