"""
主窗口类 - 应用程序的主界面
"""
from PySide6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QSplitter, QStatusBar)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QCloseEvent

from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import exception_handler_decorator


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 窗口信号
    window_closing = Signal()
    layout_changed = Signal()
    
    def __init__(self):
        super().__init__()
        
        # 窗口属性
        self._sidebar = None
        self._workspace = None
        self._main_splitter = None
        
        # 初始化UI
        self._setup_window()
        self._setup_ui()
        self._setup_connections()
        
        # 发布窗口创建事件
        event_system.emit('main_window_created', source=self)
    
    @exception_handler_decorator()
    def _setup_window(self):
        """设置窗口属性"""
        # 从配置获取窗口设置
        title = config.get('window.title', 'PySide6 高效软件框架')
        width = config.get('window.width', 1200)
        height = config.get('window.height', 800)
        min_width = config.get('window.min_width', 800)
        min_height = config.get('window.min_height', 600)
        
        # 设置窗口属性
        self.setWindowTitle(title)
        self.resize(width, height)
        self.setMinimumSize(min_width, min_height)
        
        # 设置窗口标志
        self.setWindowFlags(Qt.Window)
        
        # 居中显示
        self._center_window()
    
    def _center_window(self):
        """窗口居中显示"""
        screen = self.screen().availableGeometry()
        window_rect = self.frameGeometry()
        center_point = screen.center()
        window_rect.moveCenter(center_point)
        self.move(window_rect.topLeft())
    
    @exception_handler_decorator()
    def _setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主分割器
        self._main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self._main_splitter)
        
        # 创建侧边栏占位符
        self._create_sidebar_placeholder()
        
        # 创建工作区占位符
        self._create_workspace_placeholder()
        
        # 设置分割器比例
        self._setup_splitter_sizes()
        
        # 创建状态栏
        self._setup_status_bar()
    
    def _create_sidebar_placeholder(self):
        """创建侧边栏占位符"""
        sidebar_widget = QWidget()
        sidebar_widget.setMinimumWidth(50)  # 图标栏最小宽度
        sidebar_widget.setMaximumWidth(400)  # 侧边栏最大宽度
        
        # 临时布局
        layout = QVBoxLayout(sidebar_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加到分割器
        self._main_splitter.addWidget(sidebar_widget)
        self._sidebar = sidebar_widget
    
    def _create_workspace_placeholder(self):
        """创建工作区占位符"""
        workspace_widget = QWidget()
        workspace_widget.setMinimumWidth(400)
        
        # 临时布局
        layout = QVBoxLayout(workspace_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加到分割器
        self._main_splitter.addWidget(workspace_widget)
        self._workspace = workspace_widget
    
    def _setup_splitter_sizes(self):
        """设置分割器大小"""
        # 从配置获取侧边栏宽度
        sidebar_width = config.get('sidebar.default_width', 300)
        total_width = self.width()
        workspace_width = total_width - sidebar_width
        
        # 设置分割器比例
        self._main_splitter.setSizes([sidebar_width, workspace_width])
        
        # 设置分割器属性
        self._main_splitter.setChildrenCollapsible(False)
        self._main_splitter.setHandleWidth(3)
    
    def _setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 显示就绪状态
        status_bar.showMessage("就绪", 2000)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接分割器信号
        self._main_splitter.splitterMoved.connect(self._on_splitter_moved)
        
        # 订阅配置变化事件
        event_system.subscribe('config_changed', self._on_config_changed)
        
        # 订阅主题变化事件
        event_system.subscribe('theme_changed', self._on_theme_changed)
    
    def _on_splitter_moved(self, pos, index):
        """分割器移动处理"""
        sizes = self._main_splitter.sizes()
        
        # 发布布局变化事件
        event_system.emit('layout_changed', {
            'sidebar_width': sizes[0],
            'workspace_width': sizes[1]
        }, source=self)
        
        self.layout_changed.emit()
    
    def _on_config_changed(self, event):
        """配置变化处理"""
        # 重新应用窗口设置
        if 'window' in str(event.data):
            self._apply_window_config()
    
    def _on_theme_changed(self, event):
        """主题变化处理"""
        # 主题变化由应用程序类处理
        pass
    
    def _apply_window_config(self):
        """应用窗口配置"""
        title = config.get('window.title', 'PySide6 高效软件框架')
        self.setWindowTitle(title)
    
    def set_sidebar(self, sidebar_widget):
        """设置侧边栏组件"""
        if self._sidebar:
            # 替换现有侧边栏
            index = self._main_splitter.indexOf(self._sidebar)
            self._main_splitter.replaceWidget(index, sidebar_widget)
            self._sidebar.deleteLater()
        
        self._sidebar = sidebar_widget
        
        # 重新设置分割器大小
        self._setup_splitter_sizes()
    
    def set_workspace(self, workspace_widget):
        """设置工作区组件"""
        if self._workspace:
            # 替换现有工作区
            index = self._main_splitter.indexOf(self._workspace)
            self._main_splitter.replaceWidget(index, workspace_widget)
            self._workspace.deleteLater()
        
        self._workspace = workspace_widget
    
    def get_sidebar(self):
        """获取侧边栏组件"""
        return self._sidebar
    
    def get_workspace(self):
        """获取工作区组件"""
        return self._workspace
    
    def show_status_message(self, message: str, timeout: int = 0):
        """显示状态栏消息"""
        self.statusBar().showMessage(message, timeout)
    
    def closeEvent(self, event: QCloseEvent):
        """窗口关闭事件"""
        # 发布窗口关闭信号
        self.window_closing.emit()
        event_system.emit('main_window_closing', source=self)
        
        # 保存窗口状态
        self._save_window_state()
        
        # 接受关闭事件
        event.accept()
    
    def _save_window_state(self):
        """保存窗口状态"""
        # 保存窗口大小和位置
        geometry = self.geometry()
        window_state = {
            'x': geometry.x(),
            'y': geometry.y(),
            'width': geometry.width(),
            'height': geometry.height(),
            'maximized': self.isMaximized()
        }
        
        # 保存分割器状态
        sizes = self._main_splitter.sizes()
        splitter_state = {
            'sidebar_width': sizes[0],
            'workspace_width': sizes[1]
        }
        
        # 更新配置
        config.set('window.last_geometry', window_state)
        config.set('sidebar.last_width', sizes[0])
        
        # 发布状态保存事件
        event_system.emit('window_state_saved', {
            'window': window_state,
            'splitter': splitter_state
        }, source=self)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        # 恢复窗口几何状态
        last_geometry = config.get('window.last_geometry')
        if last_geometry:
            self.setGeometry(
                last_geometry['x'],
                last_geometry['y'],
                last_geometry['width'],
                last_geometry['height']
            )
            
            if last_geometry.get('maximized', False):
                self.showMaximized()
        
        # 恢复分割器状态
        last_sidebar_width = config.get('sidebar.last_width')
        if last_sidebar_width:
            total_width = self.width()
            workspace_width = total_width - last_sidebar_width
            self._main_splitter.setSizes([last_sidebar_width, workspace_width])
