"""
插件管理器 - 负责插件的加载、激活、停用和管理
"""
import os
import json
import importlib.util
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QMessageBox

from plugins.plugin_interface import (
    IPlugin, PluginManifest, PluginContext, PluginState,
    PluginCommand, PluginMenuItem, PluginContribution,
    SidebarPanelContribution, WorkspaceProviderContribution
)
from core.config_manager import config
from core.event_system import event_system
from core.exception_handler import exception_handler_decorator, ExceptionLevel


class PluginManager(QObject):
    """插件管理器"""
    
    # 信号
    plugin_loaded = Signal(str)  # plugin_id
    plugin_activated = Signal(str)  # plugin_id
    plugin_deactivated = Signal(str)  # plugin_id
    plugin_error = Signal(str, str)  # plugin_id, error_message
    
    def __init__(self):
        super().__init__()
        
        # 插件存储
        self._plugins: Dict[str, IPlugin] = {}
        self._manifests: Dict[str, PluginManifest] = {}
        self._contexts: Dict[str, PluginContext] = {}
        self._plugin_paths: Dict[str, str] = {}
        
        # 贡献点存储
        self._commands: Dict[str, PluginCommand] = {}
        self._menu_items: List[PluginMenuItem] = []
        self._contributions: Dict[str, List[PluginContribution]] = {}
        
        # 插件目录
        self._plugins_dir = Path(config.get('plugins.directory', 'plugins'))
        self._plugins_dir.mkdir(exist_ok=True)
        
        # 订阅事件
        event_system.subscribe('app_started', self._on_app_started)
        event_system.subscribe('app_stopping', self._on_app_stopping)
    
    @exception_handler_decorator(ExceptionLevel.ERROR)
    def discover_plugins(self) -> List[str]:
        """发现插件"""
        discovered = []
        
        if not self._plugins_dir.exists():
            return discovered
        
        # 遍历插件目录
        for item in self._plugins_dir.iterdir():
            if item.is_dir():
                manifest_path = item / "plugin.json"
                if manifest_path.exists():
                    try:
                        manifest = self._load_manifest(manifest_path)
                        if manifest:
                            self._manifests[manifest.id] = manifest
                            self._plugin_paths[manifest.id] = str(item)
                            discovered.append(manifest.id)
                    except Exception as e:
                        print(f"加载插件清单失败 {item.name}: {e}")
        
        return discovered
    
    def _load_manifest(self, manifest_path: Path) -> Optional[PluginManifest]:
        """加载插件清单"""
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证必需字段
            required_fields = ['id', 'name', 'version', 'description', 'author', 'main']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            return PluginManifest(**data)
        
        except Exception as e:
            print(f"解析插件清单失败: {e}")
            return None
    
    @exception_handler_decorator(ExceptionLevel.ERROR)
    def load_plugin(self, plugin_id: str) -> bool:
        """加载插件"""
        if plugin_id in self._plugins:
            return True  # 已加载
        
        if plugin_id not in self._manifests:
            print(f"插件清单不存在: {plugin_id}")
            return False
        
        manifest = self._manifests[plugin_id]
        plugin_path = Path(self._plugin_paths[plugin_id])
        
        try:
            # 构建主模块路径
            main_file = plugin_path / manifest.main
            if not main_file.exists():
                raise FileNotFoundError(f"主模块文件不存在: {main_file}")
            
            # 动态导入插件模块
            spec = importlib.util.spec_from_file_location(
                f"plugin_{plugin_id}", main_file
            )
            module = importlib.util.module_from_spec(spec)
            
            # 添加插件路径到sys.path
            plugin_path_str = str(plugin_path)
            if plugin_path_str not in sys.path:
                sys.path.insert(0, plugin_path_str)
            
            try:
                spec.loader.exec_module(module)
            finally:
                # 移除插件路径
                if plugin_path_str in sys.path:
                    sys.path.remove(plugin_path_str)
            
            # 查找插件类
            plugin_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, IPlugin) and 
                    attr != IPlugin):
                    plugin_class = attr
                    break
            
            if not plugin_class:
                raise ValueError("未找到插件类")
            
            # 创建插件实例
            plugin_instance = plugin_class()
            
            # 创建插件上下文
            context = PluginContext(self, manifest)
            plugin_instance._set_context(context)
            
            # 保存插件
            self._plugins[plugin_id] = plugin_instance
            self._contexts[plugin_id] = context
            
            # 发布加载事件
            self.plugin_loaded.emit(plugin_id)
            event_system.emit('plugin_loaded', {
                'plugin_id': plugin_id,
                'manifest': manifest
            }, source=self)
            
            return True
        
        except Exception as e:
            error_msg = f"加载插件失败 {plugin_id}: {e}"
            print(error_msg)
            self.plugin_error.emit(plugin_id, str(e))
            return False
    
    @exception_handler_decorator(ExceptionLevel.ERROR)
    def activate_plugin(self, plugin_id: str) -> bool:
        """激活插件"""
        if plugin_id not in self._plugins:
            if not self.load_plugin(plugin_id):
                return False
        
        plugin = self._plugins[plugin_id]
        context = self._contexts[plugin_id]
        
        if plugin.state == PluginState.ACTIVE:
            return True  # 已激活
        
        try:
            # 检查依赖
            if not self._check_dependencies(plugin_id):
                return False
            
            # 激活插件
            plugin.activate(context)
            plugin._set_state(PluginState.ACTIVE)
            
            # 发布激活事件
            self.plugin_activated.emit(plugin_id)
            event_system.emit('plugin_activated', {
                'plugin_id': plugin_id
            }, source=self)
            
            return True
        
        except Exception as e:
            error_msg = f"激活插件失败 {plugin_id}: {e}"
            print(error_msg)
            plugin._set_state(PluginState.ERROR)
            self.plugin_error.emit(plugin_id, str(e))
            return False
    
    @exception_handler_decorator(ExceptionLevel.ERROR)
    def deactivate_plugin(self, plugin_id: str) -> bool:
        """停用插件"""
        if plugin_id not in self._plugins:
            return True  # 未加载
        
        plugin = self._plugins[plugin_id]
        
        if plugin.state != PluginState.ACTIVE:
            return True  # 未激活
        
        try:
            # 停用插件
            plugin.deactivate()
            plugin._set_state(PluginState.INACTIVE)
            
            # 清理贡献点
            self._cleanup_plugin_contributions(plugin_id)
            
            # 发布停用事件
            self.plugin_deactivated.emit(plugin_id)
            event_system.emit('plugin_deactivated', {
                'plugin_id': plugin_id
            }, source=self)
            
            return True
        
        except Exception as e:
            error_msg = f"停用插件失败 {plugin_id}: {e}"
            print(error_msg)
            self.plugin_error.emit(plugin_id, str(e))
            return False
    
    def _check_dependencies(self, plugin_id: str) -> bool:
        """检查插件依赖"""
        manifest = self._manifests[plugin_id]
        
        for dep_id, dep_version in manifest.dependencies.items():
            if dep_id not in self._plugins:
                print(f"插件 {plugin_id} 依赖 {dep_id} 未安装")
                return False
            
            dep_plugin = self._plugins[dep_id]
            if dep_plugin.state != PluginState.ACTIVE:
                # 尝试激活依赖插件
                if not self.activate_plugin(dep_id):
                    print(f"插件 {plugin_id} 依赖 {dep_id} 激活失败")
                    return False
        
        return True
    
    def _cleanup_plugin_contributions(self, plugin_id: str):
        """清理插件贡献点"""
        # 清理命令
        commands_to_remove = [cmd_id for cmd_id, cmd in self._commands.items() 
                             if cmd.plugin_id == plugin_id]
        for cmd_id in commands_to_remove:
            del self._commands[cmd_id]
        
        # 清理菜单项
        self._menu_items = [item for item in self._menu_items 
                           if item.plugin_id != plugin_id]
        
        # 清理其他贡献点
        if plugin_id in self._contributions:
            for contribution in self._contributions[plugin_id]:
                contribution.dispose()
            del self._contributions[plugin_id]
    
    def register_command(self, plugin_id: str, command_id: str, 
                        callback: Callable, title: str = "", 
                        description: str = "") -> str:
        """注册命令"""
        full_command_id = f"{plugin_id}.{command_id}"
        command = PluginCommand(plugin_id, command_id, callback, title, description)
        self._commands[full_command_id] = command
        
        event_system.emit('command_registered', {
            'plugin_id': plugin_id,
            'command_id': full_command_id,
            'title': title
        }, source=self)
        
        return full_command_id
    
    def execute_command(self, command_id: str, *args, **kwargs) -> Any:
        """执行命令"""
        if command_id in self._commands:
            command = self._commands[command_id]
            return command.execute(*args, **kwargs)
        else:
            raise ValueError(f"命令不存在: {command_id}")
    
    def register_menu_item(self, plugin_id: str, menu_path: str, 
                          command_id: str, title: str = "", 
                          icon: str = None) -> PluginMenuItem:
        """注册菜单项"""
        menu_item = PluginMenuItem(plugin_id, menu_path, command_id, title, icon)
        self._menu_items.append(menu_item)
        
        event_system.emit('menu_item_registered', {
            'plugin_id': plugin_id,
            'menu_path': menu_path,
            'command_id': command_id,
            'title': title
        }, source=self)
        
        return menu_item

    def register_sidebar_panel(self, plugin_id: str, panel_id: str,
                              widget, title: str = "", icon: str = None):
        """注册侧边栏面板"""
        contribution = SidebarPanelContribution(plugin_id, panel_id, widget, title, icon)

        if plugin_id not in self._contributions:
            self._contributions[plugin_id] = []
        self._contributions[plugin_id].append(contribution)

        # 发布事件给UI层处理
        event_system.emit('sidebar_panel_registered', {
            'plugin_id': plugin_id,
            'panel_id': panel_id,
            'widget': widget,
            'title': title,
            'icon': icon
        }, source=self)

        return contribution

    def register_workspace_provider(self, plugin_id: str, provider_id: str, provider):
        """注册工作区提供者"""
        contribution = WorkspaceProviderContribution(plugin_id, provider_id, provider)

        if plugin_id not in self._contributions:
            self._contributions[plugin_id] = []
        self._contributions[plugin_id].append(contribution)

        event_system.emit('workspace_provider_registered', {
            'plugin_id': plugin_id,
            'provider_id': provider_id,
            'provider': provider
        }, source=self)

        return contribution

    def show_message(self, message: str, level: str = "info", plugin_id: str = ""):
        """显示消息"""
        # 发布消息事件给UI层处理
        event_system.emit('plugin_message', {
            'message': message,
            'level': level,
            'plugin_id': plugin_id
        }, source=self)

    def get_plugin_path(self, plugin_id: str) -> str:
        """获取插件路径"""
        return self._plugin_paths.get(plugin_id, "")

    def get_plugin_manifest(self, plugin_id: str) -> Optional[PluginManifest]:
        """获取插件清单"""
        return self._manifests.get(plugin_id)

    def get_plugin_state(self, plugin_id: str) -> PluginState:
        """获取插件状态"""
        if plugin_id in self._plugins:
            return self._plugins[plugin_id].state
        return PluginState.INACTIVE

    def get_all_plugins(self) -> Dict[str, PluginManifest]:
        """获取所有插件清单"""
        return self._manifests.copy()

    def get_active_plugins(self) -> List[str]:
        """获取所有激活的插件ID"""
        return [pid for pid, plugin in self._plugins.items()
                if plugin.state == PluginState.ACTIVE]

    def get_commands(self) -> Dict[str, PluginCommand]:
        """获取所有命令"""
        return self._commands.copy()

    def get_menu_items(self) -> List[PluginMenuItem]:
        """获取所有菜单项"""
        return self._menu_items.copy()

    def auto_load_plugins(self):
        """自动加载插件"""
        if not config.get('plugins.auto_load', True):
            return

        # 发现插件
        discovered = self.discover_plugins()
        print(f"发现 {len(discovered)} 个插件")

        # 加载和激活插件
        for plugin_id in discovered:
            manifest = self._manifests[plugin_id]

            # 检查激活事件
            if self._should_activate_plugin(manifest):
                if self.load_plugin(plugin_id):
                    self.activate_plugin(plugin_id)
                    print(f"插件已激活: {plugin_id}")
                else:
                    print(f"插件加载失败: {plugin_id}")

    def _should_activate_plugin(self, manifest: PluginManifest) -> bool:
        """检查是否应该激活插件"""
        # 检查激活事件
        activation_events = manifest.activation_events

        if "*" in activation_events:
            return True  # 总是激活

        # 这里可以根据具体的激活事件来判断
        # 例如：onLanguage:python, onCommand:myCommand 等
        return False

    def _on_app_started(self, event):
        """应用启动事件处理"""
        self.auto_load_plugins()

    def _on_app_stopping(self, event):
        """应用停止事件处理"""
        # 停用所有插件
        active_plugins = self.get_active_plugins()
        for plugin_id in active_plugins:
            self.deactivate_plugin(plugin_id)

        # 清理资源
        for context in self._contexts.values():
            context.dispose()


# 全局插件管理器实例
plugin_manager = PluginManager()
