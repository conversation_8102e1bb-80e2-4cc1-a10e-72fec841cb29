"""
事件系统 - 基于观察者模式的事件管理
支持事件的发布、订阅和取消订阅
"""
from typing import Any, Callable, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import weakref
import threading


class EventPriority(Enum):
    """事件优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class Event:
    """事件数据类"""
    name: str
    data: Any = None
    source: Optional[Any] = None
    priority: EventPriority = EventPriority.NORMAL
    
    def __post_init__(self):
        self.timestamp = threading.current_thread().ident


class EventHandler:
    """事件处理器包装类"""
    
    def __init__(self, callback: Callable, priority: EventPriority = EventPriority.NORMAL, 
                 once: bool = False, weak_ref: bool = True):
        self.priority = priority
        self.once = once
        self.call_count = 0
        
        if weak_ref and hasattr(callback, '__self__'):
            # 对于实例方法使用弱引用
            self._callback_ref = weakref.WeakMethod(callback)
        else:
            # 对于函数或静态方法直接引用
            self._callback_ref = callback
    
    @property
    def callback(self):
        """获取回调函数"""
        if hasattr(self._callback_ref, '__call__'):
            return self._callback_ref
        else:
            # 弱引用情况
            return self._callback_ref()
    
    def is_valid(self) -> bool:
        """检查处理器是否有效"""
        callback = self.callback
        return callback is not None
    
    def __call__(self, event: Event) -> bool:
        """调用事件处理器"""
        callback = self.callback
        if callback is None:
            return False
        
        try:
            callback(event)
            self.call_count += 1
            return True
        except Exception as e:
            print(f"事件处理器执行错误: {e}")
            return False


class EventSystem:
    """事件系统 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._handlers: Dict[str, List[EventHandler]] = {}
            self._event_queue: List[Event] = []
            self._processing = False
            self._lock = threading.RLock()
            self._initialized = True
    
    def subscribe(self, event_name: str, callback: Callable, 
                  priority: EventPriority = EventPriority.NORMAL,
                  once: bool = False, weak_ref: bool = True) -> EventHandler:
        """订阅事件"""
        with self._lock:
            if event_name not in self._handlers:
                self._handlers[event_name] = []
            
            handler = EventHandler(callback, priority, once, weak_ref)
            self._handlers[event_name].append(handler)
            
            # 按优先级排序
            self._handlers[event_name].sort(key=lambda h: h.priority.value, reverse=True)
            
            return handler
    
    def unsubscribe(self, event_name: str, handler: EventHandler = None, 
                    callback: Callable = None):
        """取消订阅事件"""
        with self._lock:
            if event_name not in self._handlers:
                return
            
            handlers = self._handlers[event_name]
            
            if handler:
                # 移除特定处理器
                if handler in handlers:
                    handlers.remove(handler)
            elif callback:
                # 移除特定回调函数的处理器
                to_remove = []
                for h in handlers:
                    if h.callback == callback:
                        to_remove.append(h)
                for h in to_remove:
                    handlers.remove(h)
            else:
                # 移除所有处理器
                handlers.clear()
            
            # 清理无效的处理器
            self._cleanup_handlers(event_name)
    
    def _cleanup_handlers(self, event_name: str):
        """清理无效的事件处理器"""
        if event_name in self._handlers:
            valid_handlers = [h for h in self._handlers[event_name] if h.is_valid()]
            self._handlers[event_name] = valid_handlers
    
    def emit(self, event_name: str, data: Any = None, source: Any = None,
             priority: EventPriority = EventPriority.NORMAL, immediate: bool = False):
        """发布事件"""
        event = Event(event_name, data, source, priority)
        
        if immediate:
            self._process_event(event)
        else:
            with self._lock:
                self._event_queue.append(event)
                # 按优先级排序事件队列
                self._event_queue.sort(key=lambda e: e.priority.value, reverse=True)
    
    def _process_event(self, event: Event):
        """处理单个事件"""
        if event.name not in self._handlers:
            return
        
        # 清理无效处理器
        self._cleanup_handlers(event.name)
        
        handlers = self._handlers[event.name][:]  # 创建副本避免并发修改
        to_remove = []
        
        for handler in handlers:
            if not handler.is_valid():
                to_remove.append(handler)
                continue
            
            success = handler(event)
            
            # 移除一次性处理器
            if handler.once and success:
                to_remove.append(handler)
        
        # 移除无效或一次性处理器
        with self._lock:
            for handler in to_remove:
                if handler in self._handlers[event.name]:
                    self._handlers[event.name].remove(handler)
    
    def process_events(self):
        """处理事件队列"""
        if self._processing:
            return
        
        self._processing = True
        try:
            while True:
                with self._lock:
                    if not self._event_queue:
                        break
                    event = self._event_queue.pop(0)
                
                self._process_event(event)
        finally:
            self._processing = False
    
    def clear_all(self):
        """清空所有事件处理器"""
        with self._lock:
            self._handlers.clear()
            self._event_queue.clear()
    
    def get_handler_count(self, event_name: str) -> int:
        """获取事件处理器数量"""
        if event_name in self._handlers:
            self._cleanup_handlers(event_name)
            return len(self._handlers[event_name])
        return 0


# 全局事件系统实例
event_system = EventSystem()
