2025-08-31 18:10:26,748 - <PERSON><PERSON><PERSON><PERSON><PERSON> - CRITICAL - 异常类型: ImportError
异常消息: attempted relative import beyond top-level package
上下文: {'source': 'global_hook'}
堆栈跟踪:
NoneType: None

2025-08-31 18:11:36,600 - <PERSON><PERSON><PERSON><PERSON><PERSON> - CRITICAL - 异常类型: ImportError
异常消息: attempted relative import beyond top-level package
上下文: {'source': 'global_hook'}
堆栈跟踪:
NoneType: None

2025-08-31 18:11:49,288 - ExceptionHandler - CRITICAL - 异常类型: ImportError
异常消息: attempted relative import beyond top-level package
上下文: {'source': 'global_hook'}
堆栈跟踪:
NoneType: None

2025-08-31 18:12:05,280 - ExceptionHandler - ERROR - 异常类型: ValueError
异常消息: 测试异常
上下文: {'function': 'error_function', 'args': (), 'kwargs': {}}
堆栈跟踪:
Traceback (most recent call last):
  File "e:\7-pc\work\myapp\src\core\exception_handler.py", line 182, in safe_call
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "e:\7-pc\work\myapp\test_framework.py", line 70, in error_function
    raise ValueError("测试异常")
ValueError: 测试异常

