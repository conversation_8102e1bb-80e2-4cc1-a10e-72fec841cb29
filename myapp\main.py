#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MyApp - 基于PySide6的高效软件框架
主入口文件
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.application import Application
from core.exception_handler import setup_global_exception_handler


def main():
    """应用程序主入口"""
    try:
        # 设置全局异常处理器
        setup_global_exception_handler()

        # 创建并运行应用程序
        app = Application(sys.argv)
        return app.run()

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())